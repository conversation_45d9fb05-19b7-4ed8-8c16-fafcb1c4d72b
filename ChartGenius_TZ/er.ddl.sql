
-- A.2 · Autonomous JSON DB schema for Chart Genius

CREATE COLLECTION users
  JSON_SCHEMA {
    "bsonType": "object",
    "required": ["tg_id", "role"],
    "properties": {
      "tg_id": { "bsonType": "string", "description": "Telegram ID" },
      "email": { "bsonType": "string" },
      "role": { "enum": ["user","premium","vip","admin"] },
      "tier": { "bsonType": "string" },
      "exp_ts": { "bsonType": "long" }
    }
  };

CREATE UNIQUE INDEX idx_users_tgid ON users(tg_id);

CREATE COLLECTION subscriptions
  JSON_SCHEMA {
    "bsonType": "object",
    "required": ["user_id","tier","exp_ts"],
    "properties": {
      "user_id": { "bsonType": "string" },
      "tier": { "bsonType": "string" },
      "price": { "bsonType": "double" },
      "exp_ts": { "bsonType": "long" },
      "source": { "bsonType": "string" }
    }
  };

CREATE INDEX idx_subscriptions_user ON subscriptions(user_id);

CREATE COLLECTION prompts
  JSON_SCHEMA {
    "bsonType": "object",
    "required": ["name","template_md","active"],
    "properties": {
      "name": { "bsonType": "string" },
      "version": { "bsonType": "string" },
      "template_md": { "bsonType": "string" },
      "active": { "bsonType": "bool" },
      "ts": { "bsonType": "long" }
    }
  };

CREATE INDEX idx_prompts_name_active ON prompts(name, active);

CREATE COLLECTION signals
  JSON_SCHEMA {
    "bsonType": "object",
    "required": ["symbol","tf","text_md","ts"],
    "properties": {
      "symbol": { "bsonType": "string" },
      "tf": { "bsonType": "string" },
      "text_md": { "bsonType": "string" },
      "ts": { "bsonType": "long" }
    }
  };

CREATE INDEX idx_signals_symbol_tf_ts ON signals(symbol, tf, ts);

CREATE COLLECTION config
  JSON_SCHEMA {
    "bsonType": "object",
    "required": ["llm"],
    "properties": {
      "llm": { "enum": ["openai","gemini"] },
      "limits": { "bsonType": "object" }
    }
  };
