
# Chart Genius v2 — Техническое задание **R‑7**
**Дата:** 01 July 2025  **Telegram-ID супер‑админа:** **299 820 674**

> **Цель релиза:**  
> 1. Сохранить действующий функционал Chart Genius.  
> 2. Запустить современный десктоп‑Web UI и Telegram Mini App.  
> 3. Монетизация: Stripe Checkout + Telegram Payments.  
> 4. Интеграция **Gemini CLI** (бесплатный лимит, переключатель).  
> 5. Развёртывание в Always‑Free OCI.

## 0 · Введение
Полная структура, роли, процесс решения проблем — см. R‑6. Все библиотеки обновлены до latest‑stable.

## 1 · Приложения (все файлы в каталоге `docs/`)
| Код | Файл | Описание |
|-----|------|----------|
| A.1 | links.md | ссылки на доки |
| A.2 | er.png / er.ddl.sql | ER‑диаграмма AJD |
| A.3 | openapi.yaml | спецификация `/v2/*` |
| A.4 | gemini_cli_setup.md | OAuth‑настройка Gemini CLI |
| A.5 | monitoring_oci.md | метрики + алерты |
| A.6 | env_vars.md | переменные окружения |
| A.7 | a11y_checklist.md | WCAG AA чеклист |
| tokens | tokens.chartgenius.json | дизайн‑токены |
| wireframes | Figma link: **https://www.figma.com/file/REPLACE_ME/ChartGenius_Wireframes** |

*(пустые слоты будут заполнены по мере генерации er.png и загрузки макетов в Figma).*

## 2 · Wireframes
Figma содержит три pages:  
1. **Desktop 1440×1024** (5 кадров)  
2. **Mobile 360×640**  
3. **Landscape 640×360**

Компонент‑id совпадают с React‑компонентами.

## 3 · Технические требования
(см. R‑6, изменений нет, кроме удаления Redis‑кэша OHLCV).

## 4 · План спринтов
(см. R‑6; S1 — доставка макетов + OpenAPI + ER).

---

**Документ R‑7 завершён.**
