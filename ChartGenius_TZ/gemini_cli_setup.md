
# A.4 · Настройка Gemini CLI (OAuth вариант 1)

1. **Локально** (`macOS / WSL / Linux`) выполните:
   ```bash
   npm i -g @google/gemini-cli@latest
   gcli auth login
   ```
   Браузер откроет OAuth‑экран; после успешного логина в `~/.config/gemini-cli/credentials.json`
   появится токен.

2. **Загрузите** `credentials.json` в OCI Vault (тип `secret`), присвоив имя `gemini-cli-cred`.

3. **Terraform**:
   ```hcl
   resource "oci_secrets_secretbundle" "gemini" {
     secret_id = var.vault_gemini_secret_ocid
   }
   ```

4. **cg-worker Dockerfile**:
   ```Dockerfile
   ENV GEMINI_CLI_CONFIG=/secrets/cred.json
   ```

5. **KMS Rotation**  
   Токен действует 7 дней. GitHub Action `refresh-gemini-token.yml` автоматически
   обновляет секрет.
