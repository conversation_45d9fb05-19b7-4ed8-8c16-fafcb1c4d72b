openapi: 3.1.0
info:
  title: ChartGenius API v2
  version: 0.1.0
paths:
  /v2/llm/chat:
    post:
      summary: LLM chat
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                messages:
                  type: array
                  items:
                    type: string
              required:
              - messages
      responses:
        '200':
          description: LLM response
          content:
            application/json:
              schema:
                type: object
                properties:
                  text:
                    type: string
                required:
                - text
          headers:
            X-RateLimit-Remaining:
              schema:
                type: integer
        '429':
          description: Rate limit exceeded
        '503':
          description: Service unavailable
