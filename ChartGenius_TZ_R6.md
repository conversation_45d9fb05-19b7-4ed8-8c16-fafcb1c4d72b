# Chart Genius v2 — Техническое задание **R‑6**  
**Дата:** 01 июля 2025  **Telegram‑ID супер‑админа:** **299 820 674**

> **Цель релиза:**  
> 1. Сохранить действующий функционал Chart Genius (AI‑аналитика, Telegram‑бот, React‑SPA).  
> 2. Создать современный «десктоп‑Web» UI (≥1280 px) и Telegram Mini App для мобильных устройств.  
> 3. Внедрить платёжную монетизацию (Stripe Checkout + Telegram Payments).  
> 4. Интегрировать **Gemini CLI** как альтернативную LLM‑модель (бесплатно 60 RPM / 1000 RPD) с
>    переключателем из админ‑панели.  
> 5. Развёртываться в диапазоне Always‑Free Oracle Cloud (2 × Ampere A1 VM, Autonomous JSON DB,
>    Object Storage, Redis micro).

---
## Содержание
1. [Введение](#0)  
2. [Текущее состояние](#1)  
3. [Целевая архитектура](#2)  
4. [Функциональные требования](#3)  
5. [Нефункциональные требования](#4)  
6. [Тестовая стратегия](#5)  
7. [CI / CD и DevOps](#6)  
8. [План спринтов](#7)  
9. [Риски и митигации](#8)  
10. [Критерии приёмки](#9)  
11. [Приложения](#A)

---
## <a id="0"></a>0 · Введение

### 0.1 Область охвата
Документ описывает работы **релиза v2 (R‑6)**. Любой разработчик должен найти здесь
ответы **без уточняющих вопросов**.

### 0.2 Текущие роли и контакты
| Роль        | Имя / контакт          |
|-------------|------------------------|
| Супер‑админ | @Dushilov (**299 820 674**) |
| Tech Lead   | TBD                    |

### 0.3 Out of scope
* Поддержка локальных моделей (Llama 3) перенесена в backlog.  
* Рассылка краткого анализа в inline‑режиме Telegram — исключена.

### 0.4 Процесс решения проблем
1. **Репродукция** ошибки в dev‑container.  
2. **Проверка** changelog / breaking‑changes на **официальном** сайте библиотеки.  
3. **Поиск** решения в:
   * GitHub Issues проекта;
   * официальная документация;
   * verified Q&A (Stack Overflow, Discord/Slack проекта).  
4. **Фиксация** источника в Pull‑request (ссылка + краткое резюме).  
5. Нет решения через 2 ч — эскалация Tech Lead.

---
## <a id="1"></a>1 · Текущее состояние репозитория

| Слой            | Папка       | Библиотека  | Версия |
|-----------------|-------------|-------------|--------|
| Telegram‑бот    | `bot/`      | **aiogram** | 3.4.* |
| Backend API     | `backend/`  | **FastAPI** | 0.101 |
| Frontend        | `frontend/` | **React**   | 18.2 |
| LLM SDK         | `backend/providers` | **openai** | 1.30 |
| Инфра           | root        | Docker‑Compose v3 | — |

Нет Firestore/Postgres. Бот — только aiogram.

---
## <a id="2"></a>2 · Целевая архитектура

```
┌─────────── Browser ≥1280 ───────────┐
│ React 19 (RSC) + Stripe UI         │
└─────────────────────────────────────┘
          ▲  JWT / HTTPS 
┌─ Telegram Mini App (WebView) ───┐
│     aiogram bot v2 (webhook)    │
└─────────────────────────────────┘
          ▼
   ┌─ OCI LB (HTTPS) ─┐
┌──┴──────────┐ ┌────┴─────────┐
│ cg‑core     │ │ cg‑worker    │
│ FastAPI 0.115│ │ Gemini CLI   │
│ aiogram router│ │ TA‑workers   │
└──┬───────────┘ └──┬───────────┘
   │ Redis micro    │
   │ (Gemini limit) │
┌──▼──────────┐  ┌──▼──────────┐
│ Autonomous  │  │  Object     │
│  JSON DB    │  │  Storage    │
└─────────────┘  └─────────────┘
```

---
## <a id="3"></a>3 · Функциональные требования

### 3.1 Frontend (React 19 + Vite 6)
* **Layout‑Desktop** (≥1280 px): Sidebar 280 px ▸ Header 56 px ▸ 12‑grid:
  * ChartWidget (9 cols)  
  * SignalCards (3 cols)  
  * NewsFeed (12)  
  * RecentReports (12)
* **Layout‑Mobile / TWA** (≤768 px): Sidebar скрыт, Header compact.
* Дизайн: Tailwind CSS v4 + shadcn/ui + Radix Primitives.
* TanStack Query v5, Zod‑валидация, i18n (RU/EN).
* Метрики: **TTI < 1.5 с, LCP < 2 с** по 4G/High‑end.

### 3.2 Backend (FastAPI 0.115)
* Python 3.12, Pydantic v2, SQLModel.
* Сохранить `/v1/*` роуты, добавить `/v2/*`.
* Middleware‑JWT (RS256, TTL 30 min).  
* Админ‑Telegram‑ID 299 820 674 всегда получает `role=admin`.

### 3.3 Telegram‑бот (aiogram 3.4)
| Команда    | Доступ                       | Действие |
|------------|-----------------------------|----------|
| `/start`   | все                         | приветствие + кнопка «Открыть кабинет» |
| `/help`    | все                         | справка |
| `/profile` | все                         | Login Widget / WebApp |
| `/plans`   | все                         | тарифы + оплата |
| `/signals` | `premium`,`vip`             | последние 3 сигнала PNG |
| `/admin`   | role `admin` **или** TG ID 299 820 674 | меню Users·Prompts·LLM·Broadcast |

### 3.4 LLM‑Router и Gemini CLI
* Toggle `config.llm.active_model` = `"openai"` / `"gemini"`.
* **Gemini CLI** sidecar:  
  * Установка `npm i -g @google/gemini-cli@latest`.  
  * **OAuth вариант 1:** локальный `gcli auth login` → копируем `credentials.json` в OCI Secret → монтируем `/secrets/cred.json`, ENV `GEMINI_CLI_CONFIG`.  
  * **OAuth вариант 2:** Google Service Account + domain‑wide delegation *(платно)*.  
* Redis‑token‑bucket (60 RPM, 1000 RPD). При 429/503 Gemini → fallback OpenAI.

### 3.5 Данные (AJD)
| Коллекция | Поля | Индексы |
|-----------|------|---------|
| users | `id, tg_id, email?, role, tier, exp_ts` | tg_id |
| subscriptions | `id, user_id, tier, price, exp_ts, source` | user_id |
| prompts | `id, name, version, template_md, active, ts` | name, active |
| signals | `id, symbol, tf, text_md, ts` | symbol+tf+ts |
| config | `{ llm, limits }` | — |

Бэкапы: weekly‑snapshot → Object Storage.

### 3.6 Платежи
* **Stripe Checkout** (web): карты + Apple/Google Pay.  
* **Telegram Payments**: 0 % комиссии Telegram.  
* Webhook `invoice.paid` → обновление `users.role` / `tier`.

---
## <a id="4"></a>4 · Нефункциональные требования

| Атрибут      | Требование |
|--------------|------------|
| Производительность | p95 latency < 60 мс @ 2 OCPU |
| Масштаб 20 пользователей | 1.7 RPS API + 100 REST‑OHLCV/min |
| SLA инфра | ≥ 99 % (LB + VM) |
| Security | SBOM 0 critical, JWT RS256 2048‑bit |
| Логи | gzip‑rotate, хранить 90 дней |

---
## <a id="5"></a>5 · Тестовая стратегия

| Тип                   | Инструмент | Покрытие |
|-----------------------|-----------|----------|
| Backend unit          | pytest‑asyncio | ≥ 90 % |
| Frontend unit         | vitest | ≥ 90 % |
| E2E (bot + web)       | Playwright | ≥ 20 сценариев |
| Линт / формат         | ruff, eslint | 0 ошибок |
| SBOM‑скан             | Chainguard Wolfi | 0 critical |

---
## <a id="6"></a>6 · CI / CD и DevOps

* GitHub Actions: матрица `node:20-alpine`, `python:3.12-slim`.  
* SBOM‑скан hard‑fail при любых critical.  
* Terraform 0.14 → OCI Resource Manager, blue‑green deploy (DNS swap).  
* Secrets хранить в OCI Vault.

---
## <a id="7"></a>7 · План спринтов (2 нед. × 8)

| # | Цель | Критерий Done |
|---|------|---------------|
| S1 | React 19 каркас | Lighthouse > 90 |
| S2 | Stripe Checkout | tier обновляется live |
| S3 | Telegram WebApp / Payments | план покупается в TWA |
| S4 | Bot v2 команды | `/signals` выдаёт PNG |
| S5 | Admin UI | LLM switch работает |
| S6 | Gemini CLI router | fallback OK |
| S7 | Terraform stack | `terraform apply` ≤ 15 мин |
| S8 | Нагруз‑тест | err < 0.1 %, p95 < 300 мс |

---
## <a id="8"></a>8 · Риски и митигации

| ID | Риск | Влияние | Митигация |
|----|------|---------|-----------|
| R1 | Нет свободных A1 VM | задержка деплоя | резерв регионального пула |
| R2 | Изменение квот Gemini | LLM down | fallback OpenAI + алерт |
| R3 | 20 GB AJD лимит | out‑of‑storage | еженедельный pruning signals |
| R4 | Ошибки WebApp JWT | потеря авторизации | e2e tests + expiry grace 5 мин |

---
## <a id="9"></a>9 · Критерии приёмки

1. `/profile` логин через Telegram Desktop → Dashboard **≤ 1.5 с**.  
2. Оплата Stripe меняет `tier` без перезагрузки **≤ 2 с**.  
3. Переключатель LLM применяется ко всем новым запросам **≤ 30 с**.  
4. 1001‑й запрос в сутки к Gemini возвращается через OpenAI (HTTP 200).  
5. `terraform destroy` освобождает все ресурсы **без ошибок**.

---
## <a id="A"></a>Приложения

* A.1 — Список ссылок на официальную документацию библиотек.  
* A.2 — Схемы ER‑диаграмм AJD.  
* A.3 — Swagger v2 коллекция `/v2/*`.

---
**Конец документа** — R‑6 (01‑07‑2025)  
