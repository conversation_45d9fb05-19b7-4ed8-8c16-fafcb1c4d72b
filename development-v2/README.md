# ChartGenius v2 Development Environment

Полная инфраструктура разработки для ChartGenius v2 на базе современных технологий с поддержкой Oracle Cloud Always-Free.

## 🚀 Архитектура К6-R7

### Компоненты системы

- **Frontend v2** - React 19 + Tailwind CSS v4 + Vite
- **Backend v2** - FastAPI 0.115 + SQLModel + Oracle AJD
- **Bot v2** - aiogram 3.4 + WebApp поддержка
- **Мониторинг** - Prometheus + Grafana + Flower
- **Инфраструктура** - Docker Compose + Nginx + Redis

### Технологический стек

| Компонент | Технология | Версия |
|-----------|------------|--------|
| Frontend | React + TypeScript | 19.x |
| Стилизация | Tailwind CSS | 4.x |
| Сборка | Vite | 5.x |
| Backend | FastAPI | 0.115 |
| ORM | SQLModel | latest |
| База данных | PostgreSQL (dev) / Oracle AJD (prod) | 15 / latest |
| Кэш | Redis | 7.x |
| Bot | aiogram | 3.4 |
| Мониторинг | Prometheus + Grafana | latest |
| Контейнеризация | Docker + Docker Compose | latest |

## 🛠️ Быстрый старт

### Предварительные требования

- Docker 20.10+
- Docker Compose 2.0+
- Git
- 8GB RAM (рекомендуется)
- 10GB свободного места

### Установка и запуск

```bash
# Клонирование репозитория
git clone https://github.com/2511319/GeniusO4_full.git
cd GeniusO4_full/development-v2

# Настройка прав доступа к скриптам
chmod +x scripts/*.sh

# Запуск полной инфраструктуры
./scripts/start-dev.sh
```

### Остановка

```bash
# Остановка всех сервисов
./scripts/stop-dev.sh

# Остановка с удалением данных
./scripts/stop-dev.sh --volumes

# Полная очистка (данные + образы)
./scripts/stop-dev.sh --all
```

## 🌐 Доступ к сервисам

### Основные сервисы
- **Frontend**: http://localhost:3001
- **Backend API**: http://localhost:8001
- **API Docs**: http://localhost:8001/docs
- **Bot Webhook**: http://localhost:8080

### Мониторинг
- **Grafana**: http://localhost:3000 (admin/grafana_dev_pass)
- **Prometheus**: http://localhost:9090
- **Flower**: http://localhost:5555 (admin/flower_dev_pass)

### Базы данных
- **PostgreSQL**: localhost:5432 (chartgenius/chartgenius_dev_pass)
- **Redis**: localhost:6379

## 📁 Структура проекта

```
development-v2/
├── frontend-v2/          # React 19 приложение
│   ├── src/
│   ├── public/
│   ├── package.json
│   └── Dockerfile.dev
├── backend-v2/           # FastAPI 0.115 API
│   ├── app/
│   ├── requirements.txt
│   └── Dockerfile
├── bot-v2/               # aiogram 3.4 бот
│   ├── app/
│   ├── requirements.txt
│   └── Dockerfile
├── monitoring/           # Prometheus + Grafana
│   ├── prometheus/
│   └── grafana/
├── nginx/                # Reverse proxy
├── scripts/              # Скрипты управления
├── config/               # Конфигурации
└── docker-compose.yml    # Оркестрация
```

## ⚙️ Конфигурация

### Переменные окружения

Каждый компонент имеет свой `.env.development` файл:

- `frontend-v2/.env.development` - настройки фронтенда
- `backend-v2/.env.development` - настройки API
- `bot-v2/.env.development` - настройки бота

### Основные настройки

```env
# Backend
DATABASE_URL=postgresql+asyncpg://chartgenius:pass@postgres:5432/chartgenius_dev
REDIS_URL=redis://redis:6379/0
OPENAI_API_KEY=your-key
GEMINI_CLI_CONFIG=/path/to/config

# Bot
TELEGRAM_BOT_TOKEN=your-bot-token
BACKEND_URL=http://backend-v2:8001/api/v2
WEBHOOK_MODE=false

# Frontend
VITE_API_BASE_URL=http://localhost:8001/api/v2
VITE_ENABLE_DEV_TOOLS=true
```

## 🔧 Разработка

### Команды управления

```bash
# Просмотр логов всех сервисов
docker-compose logs -f

# Просмотр логов конкретного сервиса
docker-compose logs -f backend-v2

# Перезапуск сервиса
docker-compose restart backend-v2

# Доступ к контейнеру
docker-compose exec backend-v2 bash

# Просмотр статуса
docker-compose ps
```

### Разработка компонентов

#### Frontend
```bash
# Установка зависимостей
cd frontend-v2
npm install

# Запуск в dev режиме (вне Docker)
npm run dev

# Сборка
npm run build

# Тестирование
npm run test
```

#### Backend
```bash
# Установка зависимостей
cd backend-v2
pip install -r requirements.txt

# Запуск в dev режиме (вне Docker)
python main.py

# Тестирование
pytest

# Миграции
alembic upgrade head
```

#### Bot
```bash
# Установка зависимостей
cd bot-v2
pip install -r requirements.txt

# Запуск в polling режиме
python main.py

# Тестирование
pytest
```

## 📊 Мониторинг

### Метрики

Система собирает следующие метрики:

- **HTTP запросы**: количество, латентность, ошибки
- **LLM запросы**: использование провайдеров, токены
- **База данных**: запросы, соединения, производительность
- **Redis**: операции, память, производительность
- **Пользователи**: действия, регистрации, подписки
- **Бизнес**: доходы, конверсии, активность

### Дашборды Grafana

- **ChartGenius Overview** - общий обзор системы
- **API Performance** - производительность API
- **LLM Usage** - использование LLM
- **Business Metrics** - бизнес метрики

### Алерты

Настроены алерты для:
- Высокий уровень ошибок (>10%)
- Медленные ответы (>2s)
- Недоступность сервисов
- Проблемы с базой данных
- Ошибки LLM провайдеров

## 🔒 Безопасность

### Аутентификация
- JWT RS256 токены
- Telegram WebApp интеграция
- Роли пользователей (user/premium/vip/admin)

### Rate Limiting
- Redis-based ограничения
- Лимиты по тарифам
- Защита от злоупотреблений

### Мониторинг безопасности
- Логирование всех действий
- Метрики безопасности
- Аудит административных действий

## 🚀 Деплой в продакшн

### Oracle Cloud Always-Free

```bash
# Подготовка production конфигурации
cp -r development-v2 production-v2
cd production-v2

# Обновление переменных окружения
# Настройка Oracle AJD, Cloud Run, Secrets Manager

# Деплой через Cloud Build
gcloud builds submit --config cloudbuild.yaml
```

### Миграция с v1

1. Экспорт данных из v1
2. Настройка Oracle AJD
3. Миграция пользователей
4. Переключение DNS
5. Мониторинг миграции

## 🧪 Тестирование

### Unit тесты
```bash
# Frontend
cd frontend-v2 && npm run test

# Backend
cd backend-v2 && pytest

# Bot
cd bot-v2 && pytest
```

### Integration тесты
```bash
# Запуск всех тестов
./scripts/run-tests.sh

# E2E тесты
cd frontend-v2 && npm run test:e2e
```

### Load тесты
```bash
# API нагрузочное тестирование
cd backend-v2 && locust -f tests/load/locustfile.py
```

## 📚 Документация

- [API Documentation](http://localhost:8001/docs) - Swagger UI
- [Frontend Storybook](http://localhost:6006) - UI компоненты
- [Architecture Decision Records](./docs/adr/) - Архитектурные решения
- [Deployment Guide](./docs/deployment.md) - Руководство по деплою

## 🤝 Вклад в разработку

1. Создайте feature branch
2. Внесите изменения
3. Добавьте тесты
4. Обновите документацию
5. Создайте Pull Request

## 📄 Лицензия

© 2025 ChartGenius. Все права защищены.

---

**Версия**: 2.0.0-dev  
**Последнее обновление**: 2025-01-01  
**Поддержка**: <EMAIL>
