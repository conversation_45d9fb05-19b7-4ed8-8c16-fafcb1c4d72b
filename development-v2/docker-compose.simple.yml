# ChartGenius v2 Simple Development Environment
# Simplified version for quick testing

version: '3.8'

services:
  # Redis only (no PostgreSQL for now)
  redis:
    image: redis:7-alpine
    command: redis-server --appendonly yes --maxmemory 256mb --maxmemory-policy allkeys-lru
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 3s
      retries: 3

  # Backend v2 (FastAPI 0.115) with SQLite
  backend-v2:
    build:
      context: ./backend-v2
      dockerfile: Dockerfile
    ports:
      - "8001:8001"
    volumes:
      - ./backend-v2:/app
      - ./data:/app/data
    environment:
      - DEBUG=true
      - ENVIRONMENT=development
      - DATABASE_URL=sqlite+aiosqlite:///data/chartgenius.db
      - REDIS_URL=redis://redis:6379/0
      - CELERY_BROKER_URL=redis://redis:6379/1
      - CELERY_RESULT_BACKEND=redis://redis:6379/2
      - TELEGRAM_BOT_TOKEN=**********************************************
      - OPENAI_API_KEY=demo-key-for-testing
      - GEMINI_API_KEY=demo-key-for-testing
    depends_on:
      redis:
        condition: service_healthy
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8001/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Bot v2 (aiogram 3.4)
  bot-v2:
    build:
      context: ./bot-v2
      dockerfile: Dockerfile
    volumes:
      - ./bot-v2:/app
    environment:
      - DEBUG=true
      - ENVIRONMENT=development
      - BACKEND_URL=http://backend-v2:8001/api/v2
      - REDIS_URL=redis://redis:6379/3
      - WEBHOOK_MODE=false
      - TELEGRAM_BOT_TOKEN=**********************************************
      - WEBAPP_URL=http://localhost:3001
    depends_on:
      - backend-v2
      - redis
    restart: unless-stopped

  # Frontend v2 (React 19 + Vite)
  frontend-v2:
    build:
      context: ./frontend-v2
      dockerfile: Dockerfile.dev
    ports:
      - "3001:3001"
    volumes:
      - ./frontend-v2:/app
      - /app/node_modules
    environment:
      - VITE_API_BASE_URL=http://localhost:8001/api/v2
      - VITE_WS_BASE_URL=ws://localhost:8001/ws
      - VITE_ENABLE_DEV_TOOLS=true
      - VITE_ENABLE_MOCK_DATA=true
    depends_on:
      - backend-v2
    restart: unless-stopped

  # Prometheus (simplified)
  prometheus:
    image: prom/prometheus:v2.45.0
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--storage.tsdb.retention.time=1d'
      - '--web.enable-lifecycle'
    restart: unless-stopped

  # Grafana (simplified)
  grafana:
    image: grafana/grafana:10.0.0
    ports:
      - "3000:3000"
    volumes:
      - grafana_data:/var/lib/grafana
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
      - GF_USERS_ALLOW_SIGN_UP=false
    depends_on:
      - prometheus
    restart: unless-stopped

volumes:
  redis_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local
