# ChartGenius v2 Simple Development Environment
# Simplified version for quick testing

version: '3.8'

services:
  # Redis only (no PostgreSQL for now)
  redis:
    image: redis:7-alpine
    command: redis-server --appendonly yes --maxmemory 256mb --maxmemory-policy allkeys-lru
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 3s
      retries: 3

  # Backend v2 (Simple FastAPI for testing)
  backend-v2:
    image: python:3.11-slim
    ports:
      - "8001:8001"
    working_dir: /app
    command: >
      sh -c "
        pip install fastapi uvicorn[standard] &&
        cat > simple_main.py << 'EOF'
        from fastapi import FastAPI
        from fastapi.middleware.cors import CORSMiddleware
        from datetime import datetime
        import uvicorn
        import os

        app = FastAPI(title='ChartGenius v2 Backend (Simple)', version='2.0.0-dev')
        app.add_middleware(CORSMiddleware, allow_origins=['*'], allow_methods=['*'], allow_headers=['*'])

        @app.get('/health')
        async def health():
            return {'status': 'healthy', 'version': '2.0.0-dev', 'timestamp': datetime.utcnow().isoformat()}

        @app.get('/')
        async def root():
            return {'message': 'ChartGenius v2 Backend API', 'version': '2.0.0-dev', 'docs': '/docs'}

        @app.get('/api/v2/')
        async def api_root():
            return {'message': 'ChartGenius v2 API', 'version': '2.0.0'}

        @app.get('/api/v2/signals/')
        async def get_signals():
            return {'signals': [{'id': 1, 'symbol': 'BTCUSDT', 'signal_type': 'LONG', 'confidence': 0.85}], 'total': 1}

        if __name__ == '__main__':
            uvicorn.run('simple_main:app', host='0.0.0.0', port=8001, reload=True)
        EOF
        python simple_main.py
      "
    environment:
      - DEBUG=true
      - ENVIRONMENT=development
    restart: unless-stopped

  # Bot v2 (aiogram 3.4)
  bot-v2:
    build:
      context: ./bot-v2
      dockerfile: Dockerfile
    environment:
      - DEBUG=true
      - ENVIRONMENT=development
      - BACKEND_URL=http://backend-v2:8001/api/v2
      - REDIS_URL=redis://redis:6379/3
      - WEBHOOK_MODE=false
      - TELEGRAM_BOT_TOKEN=**********************************************
      - WEBAPP_URL=http://localhost:3001
    depends_on:
      - backend-v2
      - redis
    restart: unless-stopped

  # Frontend v2 (React 19 + Vite)
  frontend-v2:
    build:
      context: ./frontend-v2
      dockerfile: Dockerfile.dev
    ports:
      - "3001:3001"
    volumes:
      - frontend_node_modules:/app/node_modules
    environment:
      - VITE_API_BASE_URL=http://localhost:8001/api/v2
      - VITE_WS_BASE_URL=ws://localhost:8001/ws
      - VITE_ENABLE_DEV_TOOLS=true
      - VITE_ENABLE_MOCK_DATA=true
    depends_on:
      - backend-v2
    restart: unless-stopped

  # Prometheus (simplified)
  prometheus:
    image: prom/prometheus:v2.45.0
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--storage.tsdb.retention.time=1d'
      - '--web.enable-lifecycle'
    restart: unless-stopped

  # Grafana (simplified)
  grafana:
    image: grafana/grafana:10.0.0
    ports:
      - "3000:3000"
    volumes:
      - grafana_data:/var/lib/grafana
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
      - GF_USERS_ALLOW_SIGN_UP=false
    depends_on:
      - prometheus
    restart: unless-stopped

volumes:
  redis_data:
    driver: local
  backend_data:
    driver: local
  frontend_node_modules:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local
