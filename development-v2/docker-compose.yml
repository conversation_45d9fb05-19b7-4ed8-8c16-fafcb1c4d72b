# ChartGenius v2 Development Environment
# Docker Compose configuration for local development

version: '3.8'

services:
  # Frontend v2 (React 19 + Vite)
  frontend-v2:
    build:
      context: ./frontend-v2
      dockerfile: Dockerfile.dev
    ports:
      - "3001:3001"
    volumes:
      - ./frontend-v2:/app
      - /app/node_modules
    environment:
      - VITE_API_BASE_URL=http://localhost:8001/api/v2
      - VITE_WS_BASE_URL=ws://localhost:8001/ws
      - VITE_ENABLE_DEV_TOOLS=true
      - VITE_ENABLE_MOCK_DATA=true
    depends_on:
      - backend-v2
    networks:
      - chartgenius-network
    restart: unless-stopped

  # Backend v2 (FastAPI 0.115)
  backend-v2:
    build:
      context: ./backend-v2
      dockerfile: Dockerfile
    ports:
      - "8001:8001"
    volumes:
      - ./backend-v2:/app
    environment:
      - DEBUG=true
      - ENVIRONMENT=development
      - DATABASE_URL=postgresql+asyncpg://chartgenius:chartgenius_dev_pass@postgres:5432/chartgenius_dev
      - REDIS_URL=redis://redis:6379/0
      - CELERY_BROKER_URL=redis://redis:6379/1
      - CELERY_RESULT_BACKEND=redis://redis:6379/2
    env_file:
      - ./backend-v2/.env.development
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - chartgenius-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8001/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Bot v2 (aiogram 3.4)
  bot-v2:
    build:
      context: ./bot-v2
      dockerfile: Dockerfile
    volumes:
      - ./bot-v2:/app
    environment:
      - DEBUG=true
      - ENVIRONMENT=development
      - BACKEND_URL=http://backend-v2:8001/api/v2
      - REDIS_URL=redis://redis:6379/3
      - WEBHOOK_MODE=false
    env_file:
      - ./bot-v2/.env.development
    depends_on:
      - backend-v2
      - redis
    networks:
      - chartgenius-network
    restart: unless-stopped

  # PostgreSQL Database (for development)
  postgres:
    image: postgres:15-alpine
    environment:
      POSTGRES_DB: chartgenius_dev
      POSTGRES_USER: chartgenius
      POSTGRES_PASSWORD: chartgenius_dev_pass
      POSTGRES_INITDB_ARGS: "--encoding=UTF8 --locale=C"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/01-init.sql
      - ./scripts/seed-data.sql:/docker-entrypoint-initdb.d/02-seed.sql
    ports:
      - "5432:5432"
    networks:
      - chartgenius-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U chartgenius -d chartgenius_dev"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis (Caching, Rate Limiting, Sessions)
  redis:
    image: redis:7-alpine
    command: redis-server --appendonly yes --maxmemory 256mb --maxmemory-policy allkeys-lru
    volumes:
      - redis_data:/data
      - ./config/redis.conf:/usr/local/etc/redis/redis.conf
    ports:
      - "6379:6379"
    networks:
      - chartgenius-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 3s
      retries: 3

  # Celery Worker (Background Tasks)
  celery-worker:
    build:
      context: ./backend-v2
      dockerfile: Dockerfile
      target: production
    command: celery -A app.celery worker --loglevel=info
    volumes:
      - ./backend-v2:/app
    environment:
      - DATABASE_URL=postgresql+asyncpg://chartgenius:password@postgres:5432/chartgenius_dev
      - REDIS_URL=redis://redis:6379/0
      - CELERY_BROKER_URL=redis://redis:6379/1
      - CELERY_RESULT_BACKEND=redis://redis:6379/2
    env_file:
      - ./backend-v2/.env.development
    depends_on:
      - postgres
      - redis
    networks:
      - chartgenius-network
    restart: unless-stopped

  # Celery Flower (Task Monitoring)
  celery-flower:
    build:
      context: ./backend-v2
      dockerfile: Dockerfile
      target: production
    command: celery -A app.celery flower --port=5555
    ports:
      - "5555:5555"
    environment:
      - CELERY_BROKER_URL=redis://redis:6379/1
      - CELERY_RESULT_BACKEND=redis://redis:6379/2
    depends_on:
      - redis
      - celery-worker
    networks:
      - chartgenius-network
    restart: unless-stopped

  # Prometheus (Metrics Collection)
  prometheus:
    image: prom/prometheus:latest
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - chartgenius-network
    restart: unless-stopped

  # Grafana (Metrics Visualization)
  grafana:
    image: grafana/grafana:latest
    ports:
      - "3000:3000"
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/provisioning:/etc/grafana/provisioning
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
      - GF_USERS_ALLOW_SIGN_UP=false
    depends_on:
      - prometheus
    networks:
      - chartgenius-network
    restart: unless-stopped

  # Nginx (Reverse Proxy)
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
    depends_on:
      - frontend-v2
      - backend-v2
    networks:
      - chartgenius-network
    restart: unless-stopped

# Networks
networks:
  chartgenius-network:
    driver: bridge

# Volumes
volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local
