# ChartGenius v2 - Настройка для Windows

Инструкции по развертыванию ChartGenius v2 в Windows окружении.

## 🖥️ Предварительные требования

### Обязательные компоненты

1. **Docker Desktop for Windows**
   ```powershell
   # Скачать с официального сайта
   # https://www.docker.com/products/docker-desktop
   
   # Проверка установки
   docker --version
   docker-compose --version
   ```

2. **Git for Windows**
   ```powershell
   # Скачать с https://git-scm.com/download/win
   git --version
   ```

3. **PowerShell 7+ (рекомендуется)**
   ```powershell
   # Установка через winget
   winget install Microsoft.PowerShell
   
   # Или скачать с GitHub
   # https://github.com/PowerShell/PowerShell/releases
   ```

### Дополнительные инструменты

- **Windows Terminal** (для удобства)
- **Visual Studio Code** (для разработки)
- **WSL2** (опционально, для Linux-совместимости)

## 🚀 Быстрый старт

### 1. Клонирование репозитория

```powershell
# Клонирование
git clone https://github.com/2511319/GeniusO4_full.git
cd GeniusO4_full\development-v2

# Проверка структуры
dir
```

### 2. Настройка окружения

```powershell
# Создание директорий данных
New-Item -ItemType Directory -Force -Path "data\postgres"
New-Item -ItemType Directory -Force -Path "data\redis"
New-Item -ItemType Directory -Force -Path "data\grafana"
New-Item -ItemType Directory -Force -Path "data\prometheus"
New-Item -ItemType Directory -Force -Path "logs"

# Копирование файлов окружения (если нужно)
if (!(Test-Path "backend-v2\.env.development")) {
    Copy-Item "backend-v2\.env.development.example" "backend-v2\.env.development" -ErrorAction SilentlyContinue
}
if (!(Test-Path "bot-v2\.env.development")) {
    Copy-Item "bot-v2\.env.development.example" "bot-v2\.env.development" -ErrorAction SilentlyContinue
}
if (!(Test-Path "frontend-v2\.env.development")) {
    Copy-Item "frontend-v2\.env.development.example" "frontend-v2\.env.development" -ErrorAction SilentlyContinue
}
```

### 3. Запуск инфраструктуры

```powershell
# Запуск базовых сервисов
docker-compose up -d postgres redis

# Ожидание готовности базы данных
do {
    Start-Sleep -Seconds 2
    $dbReady = docker-compose exec -T postgres pg_isready -U chartgenius -d chartgenius_dev 2>$null
} while ($LASTEXITCODE -ne 0)

Write-Host "✅ Database is ready" -ForegroundColor Green

# Запуск приложений
docker-compose up -d backend-v2 bot-v2 frontend-v2

# Запуск мониторинга
docker-compose up -d prometheus grafana celery-flower

# Запуск прокси
docker-compose up -d nginx
```

### 4. Проверка состояния

```powershell
# Проверка статуса контейнеров
docker-compose ps

# Проверка логов
docker-compose logs --tail=50

# Проверка доступности сервисов
$services = @{
    "Frontend" = "http://localhost:3001"
    "Backend" = "http://localhost:8001/health"
    "Grafana" = "http://localhost:3000"
    "Prometheus" = "http://localhost:9090"
}

foreach ($service in $services.GetEnumerator()) {
    try {
        $response = Invoke-WebRequest -Uri $service.Value -UseBasicParsing -TimeoutSec 5
        if ($response.StatusCode -eq 200) {
            Write-Host "✅ $($service.Key): OK" -ForegroundColor Green
        }
    } catch {
        Write-Host "❌ $($service.Key): FAILED" -ForegroundColor Red
    }
}
```

## 🔧 Windows-специфичные настройки

### Docker Desktop конфигурация

1. **Включить WSL2 backend** (рекомендуется)
   - Settings → General → Use WSL 2 based engine

2. **Настроить ресурсы**
   - Settings → Resources → Advanced
   - Memory: минимум 4GB, рекомендуется 8GB
   - CPU: минимум 2 cores

3. **Настроить файловую систему**
   - Settings → Resources → File Sharing
   - Добавить диск проекта (например, D:\)

### Переменные окружения Windows

```powershell
# Установка переменных для текущей сессии
$env:COMPOSE_CONVERT_WINDOWS_PATHS = 1
$env:COMPOSE_FILE = "docker-compose.yml"

# Постоянная установка (требует перезапуск)
[Environment]::SetEnvironmentVariable("COMPOSE_CONVERT_WINDOWS_PATHS", "1", "User")
```

### Работа с путями

```powershell
# В docker-compose.yml используйте Unix-стиль путей
# Windows: D:\project\data
# Docker: /d/project/data или ./data

# Для volume mapping:
volumes:
  - ./data/postgres:/var/lib/postgresql/data
  - ./logs:/app/logs
```

## 📜 PowerShell скрипты

### Скрипт запуска (start-dev.ps1)

```powershell
# development-v2/scripts/start-dev.ps1

Write-Host "🚀 Starting ChartGenius v2 Development Environment" -ForegroundColor Blue

# Проверка Docker
if (!(Get-Command docker -ErrorAction SilentlyContinue)) {
    Write-Host "❌ Docker not found. Please install Docker Desktop." -ForegroundColor Red
    exit 1
}

# Создание директорий
$directories = @("data\postgres", "data\redis", "data\grafana", "data\prometheus", "logs")
foreach ($dir in $directories) {
    New-Item -ItemType Directory -Force -Path $dir | Out-Null
}

# Запуск сервисов
Write-Host "🗄️ Starting infrastructure..." -ForegroundColor Blue
docker-compose up -d postgres redis

Write-Host "⏳ Waiting for database..." -ForegroundColor Blue
do {
    Start-Sleep -Seconds 2
    $null = docker-compose exec -T postgres pg_isready -U chartgenius -d chartgenius_dev 2>$null
} while ($LASTEXITCODE -ne 0)

Write-Host "🚀 Starting applications..." -ForegroundColor Blue
docker-compose up -d backend-v2 bot-v2 frontend-v2

Write-Host "📊 Starting monitoring..." -ForegroundColor Blue
docker-compose up -d prometheus grafana celery-flower nginx

Write-Host "🎉 ChartGenius v2 started successfully!" -ForegroundColor Green
Write-Host ""
Write-Host "📱 Services:" -ForegroundColor Blue
Write-Host "  Frontend:     http://localhost:3001"
Write-Host "  Backend API:  http://localhost:8001"
Write-Host "  Grafana:      http://localhost:3000 (admin/grafana_dev_pass)"
Write-Host "  Prometheus:   http://localhost:9090"
```

### Скрипт остановки (stop-dev.ps1)

```powershell
# development-v2/scripts/stop-dev.ps1

param(
    [switch]$Volumes,
    [switch]$Images,
    [switch]$All
)

Write-Host "🛑 Stopping ChartGenius v2 Development Environment" -ForegroundColor Blue

if ($All) {
    $Volumes = $true
    $Images = $true
}

# Остановка сервисов
docker-compose down

if ($Volumes) {
    Write-Host "⚠️ Removing volumes (data will be lost)..." -ForegroundColor Yellow
    $confirmation = Read-Host "Are you sure? (y/N)"
    if ($confirmation -eq 'y' -or $confirmation -eq 'Y') {
        docker-compose down -v
        Write-Host "✅ Volumes removed" -ForegroundColor Green
    }
}

if ($Images) {
    Write-Host "🧹 Removing images..." -ForegroundColor Blue
    docker images --filter "reference=development-v2*" -q | ForEach-Object { docker rmi $_ -f }
}

Write-Host "✅ ChartGenius v2 stopped" -ForegroundColor Green
```

### Скрипт проверки здоровья (health-check.ps1)

```powershell
# development-v2/scripts/health-check.ps1

Write-Host "🔍 ChartGenius v2 Health Check" -ForegroundColor Blue
Write-Host "=============================" -ForegroundColor Blue

$totalChecks = 0
$passedChecks = 0

function Test-Service {
    param($Name, $Command)
    
    $script:totalChecks++
    Write-Host -NoNewline "  🔍 $Name... "
    
    try {
        $result = Invoke-Expression $Command 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ OK" -ForegroundColor Green
            $script:passedChecks++
            return $true
        }
    } catch {}
    
    Write-Host "❌ FAILED" -ForegroundColor Red
    return $false
}

function Test-HttpEndpoint {
    param($Name, $Url, $ExpectedStatus = 200)
    
    $script:totalChecks++
    Write-Host -NoNewline "  🌐 $Name... "
    
    try {
        $response = Invoke-WebRequest -Uri $Url -UseBasicParsing -TimeoutSec 5
        if ($response.StatusCode -eq $ExpectedStatus) {
            Write-Host "✅ OK ($($response.StatusCode))" -ForegroundColor Green
            $script:passedChecks++
            return $true
        }
    } catch {}
    
    Write-Host "❌ FAILED" -ForegroundColor Red
    return $false
}

# Проверка Docker
Write-Host "`n🐳 Docker Services" -ForegroundColor Blue
Test-Service "Docker daemon" "docker info"
Test-Service "Docker Compose" "docker-compose version"

# Проверка контейнеров
Write-Host "`n📦 Container Status" -ForegroundColor Blue
$services = @("postgres", "redis", "backend-v2", "frontend-v2", "bot-v2", "prometheus", "grafana", "nginx")
foreach ($service in $services) {
    Test-Service "$service container" "docker-compose ps $service | Select-String 'Up'"
}

# Проверка HTTP endpoints
Write-Host "`n🌐 HTTP Endpoints" -ForegroundColor Blue
Test-HttpEndpoint "Frontend" "http://localhost:3001"
Test-HttpEndpoint "Backend API" "http://localhost:8001"
Test-HttpEndpoint "Backend Health" "http://localhost:8001/health"
Test-HttpEndpoint "Prometheus" "http://localhost:9090"
Test-HttpEndpoint "Grafana" "http://localhost:3000/login"

# Результаты
Write-Host "`n=============================" -ForegroundColor Blue
Write-Host "📋 Health Check Summary" -ForegroundColor Blue
Write-Host "=============================" -ForegroundColor Blue
Write-Host "Total checks: $totalChecks"
Write-Host "Passed: $passedChecks" -ForegroundColor Green
Write-Host "Failed: $($totalChecks - $passedChecks)" -ForegroundColor Red

if ($passedChecks -eq $totalChecks) {
    Write-Host "`n🎉 All checks passed! System is healthy." -ForegroundColor Green
    exit 0
} else {
    Write-Host "`n⚠️ Some checks failed. System needs attention." -ForegroundColor Yellow
    exit 1
}
```

## 🔧 Troubleshooting для Windows

### Общие проблемы

1. **Docker Desktop не запускается**
   ```powershell
   # Проверка Hyper-V
   Get-WindowsOptionalFeature -Online -FeatureName Microsoft-Hyper-V
   
   # Включение Hyper-V (требует перезагрузка)
   Enable-WindowsOptionalFeature -Online -FeatureName Microsoft-Hyper-V -All
   ```

2. **Проблемы с путями**
   ```powershell
   # Использование Unix-стиль путей в docker-compose
   # Вместо: D:\project\data
   # Используйте: ./data или /d/project/data
   ```

3. **Проблемы с портами**
   ```powershell
   # Проверка занятых портов
   netstat -an | findstr ":3001"
   netstat -an | findstr ":8001"
   
   # Освобождение порта (если нужно)
   # Найти PID процесса и завершить его
   ```

4. **Проблемы с памятью**
   ```powershell
   # Увеличение лимитов Docker Desktop
   # Settings → Resources → Advanced
   # Memory: 8GB+, Swap: 2GB+
   ```

### Логи и отладка

```powershell
# Просмотр логов всех сервисов
docker-compose logs

# Просмотр логов конкретного сервиса
docker-compose logs -f backend-v2

# Подключение к контейнеру
docker-compose exec backend-v2 bash

# Проверка использования ресурсов
docker stats
```

## 📚 Дополнительные ресурсы

- [Docker Desktop for Windows](https://docs.docker.com/desktop/windows/)
- [WSL2 Setup](https://docs.microsoft.com/en-us/windows/wsl/install)
- [PowerShell 7](https://docs.microsoft.com/en-us/powershell/scripting/install/installing-powershell-on-windows)
- [Windows Terminal](https://docs.microsoft.com/en-us/windows/terminal/)

---

**Версия**: 2.0.0-dev  
**Платформа**: Windows 10/11  
**Последнее обновление**: 2025-01-01
