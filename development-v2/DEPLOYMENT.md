# ChartGenius v2 Deployment Guide

Полное руководство по развертыванию ChartGenius v2 в различных окружениях.

## 🚀 Быстрый старт

### Локальная разработка

```bash
# 1. Клонирование репозитория
git clone https://github.com/2511319/GeniusO4_full.git
cd GeniusO4_full/development-v2

# 2. Настройка прав доступа
chmod +x scripts/*.sh
./scripts/setup-permissions.sh

# 3. Запуск инфраструктуры
./scripts/start-dev.sh

# 4. Проверка состояния
./scripts/health-check.sh
```

### Доступ к сервисам

- **Frontend**: http://localhost:3001
- **Backend**: http://localhost:8001
- **<PERSON>ana**: http://localhost:3000 (admin/grafana_dev_pass)
- **Prometheus**: http://localhost:9090

## 🏗️ Архитектура развертывания

### Development Environment
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend v2   │    │   Backend v2    │    │     Bot v2      │
│   React 19      │    │   FastAPI 0.115 │    │   aiogram 3.4   │
│   Port: 3001    │    │   Port: 8001    │    │   Port: 8080    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
┌─────────────────────────────────┼─────────────────────────────────┐
│                    Nginx Reverse Proxy                            │
│                        Port: 80                                   │
└─────────────────────────────────┼─────────────────────────────────┘
                                 │
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   PostgreSQL    │    │      Redis      │    │   Monitoring    │
│   Port: 5432    │    │   Port: 6379    │    │ Grafana/Prometheus│
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### Production Environment (Oracle Cloud)
```
┌─────────────────────────────────────────────────────────────────┐
│                        Oracle Cloud                             │
│  ┌─────────────────┐    ┌─────────────────┐    ┌──────────────┐ │
│  │   Cloud Run     │    │   Cloud Run     │    │  Cloud Run   │ │
│  │   Frontend      │    │   Backend       │    │     Bot      │ │
│  └─────────────────┘    └─────────────────┘    └──────────────┘ │
│           │                       │                       │     │
│  ┌─────────────────┐    ┌─────────────────┐    ┌──────────────┐ │
│  │ Autonomous JSON │    │   Redis Cache   │    │ Secret Mgr   │ │
│  │   Database      │    │                 │    │              │ │
│  └─────────────────┘    └─────────────────┘    └──────────────┘ │
└─────────────────────────────────────────────────────────────────┘
```

## 🔧 Конфигурация окружений

### Development (.env.development)

```env
# Application
DEBUG=true
ENVIRONMENT=development

# Database (PostgreSQL for dev)
DATABASE_URL=postgresql+asyncpg://chartgenius:pass@postgres:5432/chartgenius_dev

# Redis
REDIS_URL=redis://redis:6379/0

# Telegram
TELEGRAM_BOT_TOKEN=your-dev-bot-token
WEBHOOK_MODE=false

# LLM
OPENAI_API_KEY=your-openai-key
GEMINI_CLI_CONFIG=/path/to/config

# Frontend
VITE_API_BASE_URL=http://localhost:8001/api/v2
VITE_ENABLE_DEV_TOOLS=true
```

### Production (.env.production)

```env
# Application
DEBUG=false
ENVIRONMENT=production

# Database (Oracle AJD)
DATABASE_URL=oracle+asyncpg://user:pass@host:port/service

# Redis (Oracle Cloud)
REDIS_URL=redis://redis-host:6379/0

# Telegram
TELEGRAM_BOT_TOKEN=your-prod-bot-token
WEBHOOK_MODE=true
WEBHOOK_URL=https://your-domain.com

# LLM (from Secret Manager)
OPENAI_API_KEY=${OPENAI_API_KEY}
GEMINI_CLI_CONFIG=${GEMINI_CLI_CONFIG}

# Frontend
VITE_API_BASE_URL=https://api.your-domain.com/api/v2
VITE_ENABLE_DEV_TOOLS=false
```

## 🌐 Production Deployment

### Oracle Cloud Always-Free Setup

#### 1. Подготовка Oracle Cloud

```bash
# Установка OCI CLI
curl -L https://raw.githubusercontent.com/oracle/oci-cli/master/scripts/install/install.sh | bash

# Настройка конфигурации
oci setup config

# Создание Autonomous JSON Database
oci db autonomous-database create \
  --compartment-id $COMPARTMENT_ID \
  --db-name chartgenius \
  --display-name "ChartGenius Database" \
  --cpu-core-count 1 \
  --data-storage-size-in-tbs 1 \
  --is-free-tier true
```

#### 2. Настройка Cloud Run

```bash
# Создание проекта
gcloud projects create chartgenius-prod

# Включение API
gcloud services enable run.googleapis.com
gcloud services enable cloudbuild.googleapis.com
gcloud services enable secretmanager.googleapis.com

# Создание секретов
gcloud secrets create telegram-bot-token --data-file=telegram-token.txt
gcloud secrets create openai-api-key --data-file=openai-key.txt
```

#### 3. Сборка и деплой

```bash
# Подготовка production конфигурации
cp -r development-v2 production-v2
cd production-v2

# Обновление конфигураций для production
./scripts/prepare-production.sh

# Сборка образов
gcloud builds submit --config cloudbuild.yaml

# Деплой сервисов
gcloud run deploy chartgenius-backend \
  --image gcr.io/$PROJECT_ID/chartgenius-backend:latest \
  --platform managed \
  --region europe-west1 \
  --allow-unauthenticated \
  --memory 512Mi \
  --cpu 1 \
  --min-instances 0 \
  --max-instances 10

gcloud run deploy chartgenius-frontend \
  --image gcr.io/$PROJECT_ID/chartgenius-frontend:latest \
  --platform managed \
  --region europe-west1 \
  --allow-unauthenticated

gcloud run deploy chartgenius-bot \
  --image gcr.io/$PROJECT_ID/chartgenius-bot:latest \
  --platform managed \
  --region europe-west1 \
  --allow-unauthenticated
```

### Docker Production Setup

```bash
# Создание production docker-compose
cp docker-compose.yml docker-compose.prod.yml

# Обновление для production
# - Удаление dev volumes
# - Добавление health checks
# - Настройка restart policies
# - Конфигурация SSL

# Запуск в production режиме
docker-compose -f docker-compose.prod.yml up -d
```

## 🔄 Миграция с v1

### Автоматическая миграция

```bash
# Запуск скрипта миграции
./scripts/migrate-from-v1.sh

# Проверка результатов
cat migration-backup-*/MIGRATION_SUMMARY.md

# Импорт данных
docker cp migration-backup-*/data/ development-v2_postgres_1:/tmp/
docker-compose exec postgres psql -U chartgenius -d chartgenius_dev -f /tmp/migrate-data.sql
```

### Ручная миграция

1. **Экспорт данных из v1**
```sql
-- Экспорт пользователей
COPY users TO '/tmp/users.csv' WITH CSV HEADER;

-- Экспорт подписок
COPY subscriptions TO '/tmp/subscriptions.csv' WITH CSV HEADER;

-- Экспорт сигналов
COPY signals TO '/tmp/signals.csv' WITH CSV HEADER;
```

2. **Импорт в v2**
```sql
-- Импорт пользователей
\copy users(tg_id,email,role,tier,first_name,last_name,username,created_at) FROM 'users.csv' WITH CSV HEADER;

-- Обновление последовательностей
SELECT setval('users_id_seq', (SELECT MAX(id) FROM users));
```

3. **Проверка миграции**
```bash
# Проверка количества пользователей
curl http://localhost:8001/admin/stats

# Тестирование бота
# Отправка /start в Telegram

# Проверка WebApp
# Открытие в браузере
```

## 📊 Мониторинг и логирование

### Grafana Dashboards

1. **ChartGenius Overview**
   - Системные метрики
   - HTTP запросы
   - Ошибки и латентность

2. **Business Metrics**
   - Активные пользователи
   - Подписки
   - Доходы

3. **LLM Usage**
   - Запросы к провайдерам
   - Использование токенов
   - Ошибки API

### Prometheus Alerts

```yaml
# Высокий уровень ошибок
- alert: HighErrorRate
  expr: rate(http_requests_total{status_code=~"5.."}[5m]) > 0.1
  for: 2m

# Медленные ответы
- alert: HighResponseTime
  expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m])) > 2
  for: 5m

# Недоступность сервиса
- alert: ServiceDown
  expr: up == 0
  for: 1m
```

### Логирование

```python
# Структурированные логи
import structlog

logger = structlog.get_logger(__name__)

logger.info(
    "User action",
    user_id=user.id,
    action="login",
    ip_address=request.client.host,
    user_agent=request.headers.get("user-agent"),
)
```

## 🔒 Безопасность

### SSL/TLS настройка

```nginx
server {
    listen 443 ssl http2;
    server_name your-domain.com;
    
    ssl_certificate /etc/nginx/ssl/cert.pem;
    ssl_certificate_key /etc/nginx/ssl/key.pem;
    
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;
    ssl_prefer_server_ciphers off;
    
    add_header Strict-Transport-Security "max-age=63072000" always;
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
}
```

### Secrets Management

```bash
# Google Cloud Secret Manager
gcloud secrets create telegram-bot-token --data-file=token.txt
gcloud secrets create openai-api-key --data-file=key.txt

# Использование в Cloud Run
gcloud run deploy chartgenius-backend \
  --set-env-vars="TELEGRAM_BOT_TOKEN=projects/$PROJECT_ID/secrets/telegram-bot-token/versions/latest"
```

## 🧪 Тестирование

### Unit Tests

```bash
# Backend тесты
cd backend-v2
pytest tests/ -v --cov=app

# Frontend тесты
cd frontend-v2
npm run test

# Bot тесты
cd bot-v2
pytest tests/ -v
```

### Integration Tests

```bash
# API интеграционные тесты
cd backend-v2
pytest tests/integration/ -v

# E2E тесты
cd frontend-v2
npm run test:e2e
```

### Load Testing

```bash
# Нагрузочное тестирование API
cd backend-v2
locust -f tests/load/locustfile.py --host=http://localhost:8001
```

## 🔧 Troubleshooting

### Общие проблемы

1. **Контейнеры не запускаются**
```bash
# Проверка логов
docker-compose logs [service]

# Проверка ресурсов
docker system df
docker system prune
```

2. **База данных недоступна**
```bash
# Проверка подключения
docker-compose exec postgres pg_isready

# Проверка логов
docker-compose logs postgres
```

3. **Redis недоступен**
```bash
# Проверка подключения
docker-compose exec redis redis-cli ping

# Очистка кэша
docker-compose exec redis redis-cli flushall
```

4. **LLM провайдеры недоступны**
```bash
# Проверка статуса
curl http://localhost:8001/api/v2/llm/status

# Проверка ключей API
docker-compose exec backend-v2 env | grep -E "(OPENAI|GEMINI)"
```

### Производительность

1. **Медленные запросы**
```sql
-- Анализ медленных запросов
SELECT query, mean_time, calls 
FROM pg_stat_statements 
ORDER BY mean_time DESC 
LIMIT 10;
```

2. **Высокое использование памяти**
```bash
# Мониторинг памяти
docker stats --no-stream

# Анализ использования Redis
docker-compose exec redis redis-cli info memory
```

## 📞 Поддержка

### Контакты
- **Email**: <EMAIL>
- **Telegram**: @chartgenius_support
- **GitHub Issues**: https://github.com/2511319/GeniusO4_full/issues

### Документация
- **API Docs**: http://localhost:8001/docs
- **Architecture**: ./docs/architecture.md
- **Contributing**: ./docs/contributing.md

---

**Версия**: 2.0.0-dev  
**Последнее обновление**: 2025-01-01
