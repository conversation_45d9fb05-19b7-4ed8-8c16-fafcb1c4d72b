import React, { useEffect } from 'react'
import { useLocation } from 'react-router-dom'
import { ArrowLeft, MoreVertical } from 'lucide-react'

import { useTelegramWebApp } from '@/hooks/useTelegramWebApp'
import { useAuthStore } from '@/stores/authStore'
import { cn } from '@/lib/utils'

interface TelegramWebAppLayoutProps {
  children: React.ReactNode
}

export default function TelegramWebAppLayout({ children }: TelegramWebAppLayoutProps) {
  const location = useLocation()
  const { user } = useAuthStore()
  const { 
    webApp, 
    showBackButton, 
    hideBackButton, 
    showMainButton, 
    hideMainButton,
    hapticFeedback,
    themeParams 
  } = useTelegramWebApp()

  // Configure back button based on current route
  useEffect(() => {
    if (!webApp) return

    const isRootRoute = location.pathname === '/' || location.pathname === '/dashboard'
    
    if (isRootRoute) {
      hideBackButton()
    } else {
      showBackButton(() => {
        hapticFeedback('impact', 'light')
        window.history.back()
      })
    }

    return () => {
      hideBackButton()
    }
  }, [location.pathname, webApp, showBackButton, hideBackButton, hapticFeedback])

  // Configure main button based on current route and user state
  useEffect(() => {
    if (!webApp) return

    // Hide main button by default
    hideMainButton()

    // Show specific buttons based on route
    if (location.pathname === '/plans' && user?.tier === 'free') {
      showMainButton('Выбрать план', () => {
        hapticFeedback('notification', 'success')
        // Handle plan selection
      })
    } else if (location.pathname === '/analysis' && user?.role === 'premium') {
      showMainButton('Создать анализ', () => {
        hapticFeedback('impact', 'medium')
        // Handle analysis creation
      })
    }

    return () => {
      hideMainButton()
    }
  }, [location.pathname, user, webApp, showMainButton, hideMainButton, hapticFeedback])

  // Apply Telegram theme colors
  useEffect(() => {
    if (!themeParams || Object.keys(themeParams).length === 0) return

    const root = document.documentElement
    
    // Map Telegram theme parameters to CSS variables
    if (themeParams.bg_color) {
      root.style.setProperty('--tg-bg-color', themeParams.bg_color)
    }
    if (themeParams.text_color) {
      root.style.setProperty('--tg-text-color', themeParams.text_color)
    }
    if (themeParams.hint_color) {
      root.style.setProperty('--tg-hint-color', themeParams.hint_color)
    }
    if (themeParams.link_color) {
      root.style.setProperty('--tg-link-color', themeParams.link_color)
    }
    if (themeParams.button_color) {
      root.style.setProperty('--tg-button-color', themeParams.button_color)
    }
    if (themeParams.button_text_color) {
      root.style.setProperty('--tg-button-text-color', themeParams.button_text_color)
    }
    if (themeParams.secondary_bg_color) {
      root.style.setProperty('--tg-secondary-bg-color', themeParams.secondary_bg_color)
    }

    return () => {
      // Cleanup theme variables
      root.style.removeProperty('--tg-bg-color')
      root.style.removeProperty('--tg-text-color')
      root.style.removeProperty('--tg-hint-color')
      root.style.removeProperty('--tg-link-color')
      root.style.removeProperty('--tg-button-color')
      root.style.removeProperty('--tg-button-text-color')
      root.style.removeProperty('--tg-secondary-bg-color')
    }
  }, [themeParams])

  const getPageTitle = () => {
    switch (location.pathname) {
      case '/':
      case '/dashboard':
        return 'ChartGenius'
      case '/analysis':
        return 'Анализ'
      case '/plans':
        return 'Тарифы'
      case '/profile':
        return 'Профиль'
      case '/settings':
        return 'Настройки'
      default:
        return 'ChartGenius'
    }
  }

  return (
    <div className={cn(
      "min-h-screen bg-background text-foreground",
      "twa-viewport"
    )}>
      {/* Telegram WebApp Header */}
      <header className="sticky top-0 z-50 h-12 border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="flex items-center justify-between h-full px-4">
          <div className="flex items-center gap-3">
            {location.pathname !== '/' && location.pathname !== '/dashboard' && (
              <button
                onClick={() => {
                  hapticFeedback('impact', 'light')
                  window.history.back()
                }}
                className="p-1 -ml-1 text-muted-foreground hover:text-foreground rounded-md hover:bg-accent"
              >
                <ArrowLeft className="h-5 w-5" />
              </button>
            )}
            
            <h1 className="text-lg font-semibold">
              {getPageTitle()}
            </h1>
          </div>

          {/* Header Actions */}
          <div className="flex items-center gap-2">
            {user && (
              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <span className="capitalize">{user.tier}</span>
                {user.tier !== 'free' && (
                  <div className="w-2 h-2 bg-primary rounded-full"></div>
                )}
              </div>
            )}
            
            <button className="p-1 text-muted-foreground hover:text-foreground rounded-md hover:bg-accent">
              <MoreVertical className="h-5 w-5" />
            </button>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="pb-safe">
        {children}
      </main>

      {/* Telegram WebApp specific styles */}
      <style jsx global>{`
        .twa-viewport {
          /* Use Telegram theme colors if available */
          background-color: var(--tg-bg-color, hsl(var(--background)));
          color: var(--tg-text-color, hsl(var(--foreground)));
        }
        
        /* Safe area for devices with notches */
        .pb-safe {
          padding-bottom: env(safe-area-inset-bottom);
        }
        
        /* Telegram WebApp specific button styles */
        .twa-button {
          background-color: var(--tg-button-color, hsl(var(--primary)));
          color: var(--tg-button-text-color, hsl(var(--primary-foreground)));
        }
        
        /* Telegram WebApp link styles */
        .twa-link {
          color: var(--tg-link-color, hsl(var(--primary)));
        }
        
        /* Telegram WebApp secondary background */
        .twa-secondary-bg {
          background-color: var(--tg-secondary-bg-color, hsl(var(--card)));
        }
        
        /* Telegram WebApp hint text */
        .twa-hint {
          color: var(--tg-hint-color, hsl(var(--muted-foreground)));
        }
        
        /* Disable text selection in WebApp */
        .twa-viewport * {
          -webkit-user-select: none;
          -moz-user-select: none;
          -ms-user-select: none;
          user-select: none;
        }
        
        /* Allow text selection for specific elements */
        .twa-viewport input,
        .twa-viewport textarea,
        .twa-viewport [contenteditable],
        .twa-viewport .selectable {
          -webkit-user-select: text;
          -moz-user-select: text;
          -ms-user-select: text;
          user-select: text;
        }
        
        /* Smooth scrolling for WebApp */
        .twa-viewport {
          scroll-behavior: smooth;
          -webkit-overflow-scrolling: touch;
        }
        
        /* Hide scrollbars in WebApp */
        .twa-viewport::-webkit-scrollbar {
          display: none;
        }
        
        .twa-viewport {
          -ms-overflow-style: none;
          scrollbar-width: none;
        }
      `}</style>
    </div>
  )
}
