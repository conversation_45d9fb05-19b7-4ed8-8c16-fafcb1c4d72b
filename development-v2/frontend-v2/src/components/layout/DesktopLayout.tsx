import React, { useState } from 'react'
import { Link, useLocation } from 'react-router-dom'
import { 
  BarChart3, 
  TrendingUp, 
  Settings, 
  User, 
  Bell,
  Menu,
  X,
  LogOut,
  Crown,
  Shield
} from 'lucide-react'

import { useAuthStore } from '@/stores/authStore'
import { cn } from '@/lib/utils'
import { hasPermission } from '@/types/auth'

interface DesktopLayoutProps {
  children: React.ReactNode
}

const navigation = [
  {
    name: 'Анализ',
    href: '/desktop',
    icon: BarChart3,
    permission: 'analysis:read' as const,
  },
  {
    name: 'Дашборд',
    href: '/dashboard',
    icon: TrendingUp,
    permission: 'analysis:read' as const,
  },
  {
    name: 'Тарифы',
    href: '/plans',
    icon: Crown,
    permission: null,
  },
  {
    name: 'Админ',
    href: '/admin',
    icon: Shield,
    permission: 'admin:users' as const,
  },
]

export default function DesktopLayout({ children }: DesktopLayoutProps) {
  const location = useLocation()
  const { user, logout } = useAuthStore()
  const [sidebarOpen, setSidebarOpen] = useState(true)

  const handleLogout = () => {
    logout()
  }

  const filteredNavigation = navigation.filter(item => 
    !item.permission || hasPermission(user, item.permission)
  )

  return (
    <div className="flex h-screen bg-background">
      {/* Sidebar */}
      <div className={cn(
        "flex flex-col bg-card border-r transition-all duration-300",
        sidebarOpen ? "w-[280px]" : "w-16"
      )}>
        {/* Sidebar Header */}
        <div className="flex items-center justify-between p-4 border-b">
          {sidebarOpen && (
            <div className="flex items-center gap-2">
              <BarChart3 className="h-8 w-8 text-primary" />
              <div>
                <h1 className="text-lg font-semibold">ChartGenius</h1>
                <p className="text-xs text-muted-foreground">v2.0.0-dev</p>
              </div>
            </div>
          )}
          <button
            onClick={() => setSidebarOpen(!sidebarOpen)}
            className="p-2 rounded-md hover:bg-accent"
          >
            {sidebarOpen ? (
              <X className="h-4 w-4" />
            ) : (
              <Menu className="h-4 w-4" />
            )}
          </button>
        </div>

        {/* Navigation */}
        <nav className="flex-1 p-4 space-y-2">
          {filteredNavigation.map((item) => {
            const isActive = location.pathname === item.href || 
                           (item.href === '/admin' && location.pathname.startsWith('/admin'))
            
            return (
              <Link
                key={item.name}
                to={item.href}
                className={cn(
                  "flex items-center gap-3 px-3 py-2 rounded-md text-sm font-medium transition-colors",
                  isActive
                    ? "bg-primary text-primary-foreground"
                    : "text-muted-foreground hover:text-foreground hover:bg-accent",
                  !sidebarOpen && "justify-center"
                )}
                title={!sidebarOpen ? item.name : undefined}
              >
                <item.icon className="h-4 w-4 flex-shrink-0" />
                {sidebarOpen && <span>{item.name}</span>}
              </Link>
            )
          })}
        </nav>

        {/* User Section */}
        {user && (
          <div className="p-4 border-t">
            <div className={cn(
              "flex items-center gap-3",
              !sidebarOpen && "justify-center"
            )}>
              <div className="flex items-center justify-center w-8 h-8 bg-primary text-primary-foreground rounded-full text-sm font-medium">
                {user.first_name?.[0] || user.username?.[0] || 'U'}
              </div>
              
              {sidebarOpen && (
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium truncate">
                    {user.first_name || user.username || 'Пользователь'}
                  </p>
                  <p className="text-xs text-muted-foreground capitalize">
                    {user.role} • {user.tier}
                  </p>
                </div>
              )}
            </div>

            {sidebarOpen && (
              <div className="mt-3 flex gap-2">
                <Link
                  to="/settings"
                  className="flex-1 flex items-center justify-center gap-2 px-3 py-2 text-xs bg-accent hover:bg-accent/80 rounded-md transition-colors"
                >
                  <Settings className="h-3 w-3" />
                  Настройки
                </Link>
                <button
                  onClick={handleLogout}
                  className="flex items-center justify-center px-3 py-2 text-xs text-destructive hover:bg-destructive/10 rounded-md transition-colors"
                  title="Выйти"
                >
                  <LogOut className="h-3 w-3" />
                </button>
              </div>
            )}
          </div>
        )}
      </div>

      {/* Main Content */}
      <div className="flex-1 flex flex-col overflow-hidden">
        {/* Header */}
        <header className="h-14 border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
          <div className="flex items-center justify-between h-full px-6">
            <div className="flex items-center gap-4">
              <h2 className="text-lg font-semibold">
                {location.pathname === '/desktop' && 'Анализ рынка'}
                {location.pathname === '/dashboard' && 'Дашборд'}
                {location.pathname === '/plans' && 'Тарифные планы'}
                {location.pathname.startsWith('/admin') && 'Администрирование'}
              </h2>
            </div>

            <div className="flex items-center gap-4">
              {/* Notifications */}
              <button className="relative p-2 text-muted-foreground hover:text-foreground rounded-md hover:bg-accent">
                <Bell className="h-4 w-4" />
                <span className="absolute top-1 right-1 w-2 h-2 bg-primary rounded-full"></span>
              </button>

              {/* User Menu */}
              {user && (
                <div className="flex items-center gap-2 px-3 py-1 bg-accent rounded-md">
                  <User className="h-4 w-4" />
                  <span className="text-sm font-medium">
                    {user.first_name || user.username || 'Пользователь'}
                  </span>
                </div>
              )}
            </div>
          </div>
        </header>

        {/* Main Content Area */}
        <main className="flex-1 overflow-auto p-6 bg-background">
          {children}
        </main>
      </div>
    </div>
  )
}
