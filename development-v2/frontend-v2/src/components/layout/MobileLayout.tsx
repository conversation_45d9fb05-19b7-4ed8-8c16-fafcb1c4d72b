import React, { useState } from 'react'
import { Link, useLocation } from 'react-router-dom'
import { 
  BarChart3, 
  TrendingUp, 
  Settings, 
  User, 
  Menu,
  X,
  Home,
  Crown,
  Shield
} from 'lucide-react'

import { useAuthStore } from '@/stores/authStore'
import { cn } from '@/lib/utils'
import { hasPermission } from '@/types/auth'

interface MobileLayoutProps {
  children: React.ReactNode
}

const navigation = [
  {
    name: 'Главная',
    href: '/dashboard',
    icon: Home,
    permission: null,
  },
  {
    name: 'Анализ',
    href: '/analysis',
    icon: BarChart3,
    permission: 'analysis:read' as const,
  },
  {
    name: 'Тарифы',
    href: '/plans',
    icon: Crown,
    permission: null,
  },
  {
    name: 'Админ',
    href: '/admin',
    icon: Shield,
    permission: 'admin:users' as const,
  },
]

export default function MobileLayout({ children }: MobileLayoutProps) {
  const location = useLocation()
  const { user } = useAuthStore()
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false)

  const filteredNavigation = navigation.filter(item => 
    !item.permission || hasPermission(user, item.permission)
  )

  const currentPage = filteredNavigation.find(item => 
    location.pathname === item.href || 
    (item.href === '/admin' && location.pathname.startsWith('/admin'))
  )

  return (
    <div className="flex flex-col h-screen bg-background">
      {/* Mobile Header */}
      <header className="h-14 border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="flex items-center justify-between h-full px-4">
          <div className="flex items-center gap-3">
            <button
              onClick={() => setMobileMenuOpen(true)}
              className="p-2 -ml-2 text-muted-foreground hover:text-foreground rounded-md hover:bg-accent"
            >
              <Menu className="h-5 w-5" />
            </button>
            
            <div className="flex items-center gap-2">
              <BarChart3 className="h-6 w-6 text-primary" />
              <h1 className="text-lg font-semibold">ChartGenius</h1>
            </div>
          </div>

          {user && (
            <Link
              to="/profile"
              className="flex items-center justify-center w-8 h-8 bg-primary text-primary-foreground rounded-full text-sm font-medium"
            >
              {user.first_name?.[0] || user.username?.[0] || 'U'}
            </Link>
          )}
        </div>
      </header>

      {/* Mobile Menu Overlay */}
      {mobileMenuOpen && (
        <div className="fixed inset-0 z-50 bg-background/80 backdrop-blur-sm">
          <div className="fixed inset-y-0 left-0 w-64 bg-card border-r shadow-lg">
            {/* Menu Header */}
            <div className="flex items-center justify-between p-4 border-b">
              <div className="flex items-center gap-2">
                <BarChart3 className="h-6 w-6 text-primary" />
                <div>
                  <h2 className="text-lg font-semibold">ChartGenius</h2>
                  <p className="text-xs text-muted-foreground">v2.0.0-dev</p>
                </div>
              </div>
              <button
                onClick={() => setMobileMenuOpen(false)}
                className="p-2 text-muted-foreground hover:text-foreground rounded-md hover:bg-accent"
              >
                <X className="h-5 w-5" />
              </button>
            </div>

            {/* Navigation */}
            <nav className="p-4 space-y-2">
              {filteredNavigation.map((item) => {
                const isActive = location.pathname === item.href || 
                               (item.href === '/admin' && location.pathname.startsWith('/admin'))
                
                return (
                  <Link
                    key={item.name}
                    to={item.href}
                    onClick={() => setMobileMenuOpen(false)}
                    className={cn(
                      "flex items-center gap-3 px-3 py-3 rounded-md text-sm font-medium transition-colors",
                      isActive
                        ? "bg-primary text-primary-foreground"
                        : "text-muted-foreground hover:text-foreground hover:bg-accent"
                    )}
                  >
                    <item.icon className="h-5 w-5" />
                    <span>{item.name}</span>
                  </Link>
                )
              })}
            </nav>

            {/* User Section */}
            {user && (
              <div className="absolute bottom-0 left-0 right-0 p-4 border-t bg-card">
                <div className="flex items-center gap-3 mb-3">
                  <div className="flex items-center justify-center w-10 h-10 bg-primary text-primary-foreground rounded-full text-sm font-medium">
                    {user.first_name?.[0] || user.username?.[0] || 'U'}
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium truncate">
                      {user.first_name || user.username || 'Пользователь'}
                    </p>
                    <p className="text-xs text-muted-foreground capitalize">
                      {user.role} • {user.tier}
                    </p>
                  </div>
                </div>

                <div className="flex gap-2">
                  <Link
                    to="/settings"
                    onClick={() => setMobileMenuOpen(false)}
                    className="flex-1 flex items-center justify-center gap-2 px-3 py-2 text-sm bg-accent hover:bg-accent/80 rounded-md transition-colors"
                  >
                    <Settings className="h-4 w-4" />
                    Настройки
                  </Link>
                  <Link
                    to="/profile"
                    onClick={() => setMobileMenuOpen(false)}
                    className="flex items-center justify-center px-3 py-2 text-sm bg-accent hover:bg-accent/80 rounded-md transition-colors"
                  >
                    <User className="h-4 w-4" />
                  </Link>
                </div>
              </div>
            )}
          </div>

          {/* Overlay to close menu */}
          <div 
            className="absolute inset-0 -z-10"
            onClick={() => setMobileMenuOpen(false)}
          />
        </div>
      )}

      {/* Main Content */}
      <main className="flex-1 overflow-auto">
        {children}
      </main>

      {/* Bottom Navigation */}
      <nav className="border-t bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="flex">
          {filteredNavigation.slice(0, 4).map((item) => {
            const isActive = location.pathname === item.href || 
                           (item.href === '/admin' && location.pathname.startsWith('/admin'))
            
            return (
              <Link
                key={item.name}
                to={item.href}
                className={cn(
                  "flex-1 flex flex-col items-center justify-center py-2 px-1 text-xs transition-colors",
                  isActive
                    ? "text-primary"
                    : "text-muted-foreground hover:text-foreground"
                )}
              >
                <item.icon className="h-5 w-5 mb-1" />
                <span className="truncate">{item.name}</span>
              </Link>
            )
          })}
        </div>
      </nav>
    </div>
  )
}
