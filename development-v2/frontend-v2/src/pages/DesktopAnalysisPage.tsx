import React, { useState } from 'react'
import { 
  BarChart3, 
  TrendingUp, 
  <PERSON>ting<PERSON>, 
  Play,
  Download,
  RefreshCw,
  Zap
} from 'lucide-react'

import { cn } from '@/lib/utils'

// Mock chart component placeholder
const ChartWidget = () => (
  <div className="w-full h-full bg-card rounded-lg border flex items-center justify-center">
    <div className="text-center space-y-2">
      <BarChart3 className="h-12 w-12 text-muted-foreground mx-auto" />
      <p className="text-muted-foreground">График будет здесь</p>
      <p className="text-xs text-muted-foreground">
        Интеграция с lightweight-charts
      </p>
    </div>
  </div>
)

// Mock signal cards component
const SignalCards = () => (
  <div className="space-y-4">
    <h3 className="text-lg font-semibold">Торговые сигналы</h3>
    
    {[1, 2, 3].map((i) => (
      <div key={i} className="p-4 bg-card rounded-lg border space-y-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 bg-success rounded-full"></div>
            <span className="font-medium">BTCUSDT</span>
            <span className="text-xs bg-muted px-2 py-1 rounded">4h</span>
          </div>
          <span className="text-sm text-success">LONG</span>
        </div>
        
        <div className="space-y-2">
          <div className="flex justify-between text-sm">
            <span className="text-muted-foreground">Цена входа:</span>
            <span className="font-medium">$67,420</span>
          </div>
          <div className="flex justify-between text-sm">
            <span className="text-muted-foreground">Take Profit:</span>
            <span className="text-success">$69,500</span>
          </div>
          <div className="flex justify-between text-sm">
            <span className="text-muted-foreground">Stop Loss:</span>
            <span className="text-destructive">$65,800</span>
          </div>
        </div>
        
        <div className="flex items-center gap-2">
          <span className="text-xs text-muted-foreground">Уверенность:</span>
          <div className="flex-1 h-2 bg-muted rounded-full overflow-hidden">
            <div className="h-full bg-success rounded-full w-[85%]"></div>
          </div>
          <span className="text-xs font-medium">85%</span>
        </div>
      </div>
    ))}
  </div>
)

export default function DesktopAnalysisPage() {
  const [selectedSymbol, setSelectedSymbol] = useState('BTCUSDT')
  const [selectedTimeframe, setSelectedTimeframe] = useState('4h')
  const [isAnalyzing, setIsAnalyzing] = useState(false)

  const symbols = ['BTCUSDT', 'ETHUSDT', 'ADAUSDT', 'BNBUSDT', 'SOLUSDT']
  const timeframes = ['1m', '5m', '15m', '1h', '4h', '1d', '1w']

  const handleAnalyze = async () => {
    setIsAnalyzing(true)
    // Simulate analysis
    setTimeout(() => {
      setIsAnalyzing(false)
    }, 3000)
  }

  return (
    <div className="h-full space-y-6">
      {/* Analysis Controls */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <div className="flex items-center gap-2">
            <label className="text-sm font-medium">Символ:</label>
            <select 
              value={selectedSymbol}
              onChange={(e) => setSelectedSymbol(e.target.value)}
              className="px-3 py-2 bg-background border rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-primary"
            >
              {symbols.map(symbol => (
                <option key={symbol} value={symbol}>{symbol}</option>
              ))}
            </select>
          </div>

          <div className="flex items-center gap-2">
            <label className="text-sm font-medium">Таймфрейм:</label>
            <select 
              value={selectedTimeframe}
              onChange={(e) => setSelectedTimeframe(e.target.value)}
              className="px-3 py-2 bg-background border rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-primary"
            >
              {timeframes.map(tf => (
                <option key={tf} value={tf}>{tf}</option>
              ))}
            </select>
          </div>
        </div>

        <div className="flex items-center gap-2">
          <button className="p-2 text-muted-foreground hover:text-foreground rounded-md hover:bg-accent">
            <RefreshCw className="h-4 w-4" />
          </button>
          
          <button className="p-2 text-muted-foreground hover:text-foreground rounded-md hover:bg-accent">
            <Download className="h-4 w-4" />
          </button>
          
          <button className="p-2 text-muted-foreground hover:text-foreground rounded-md hover:bg-accent">
            <Settings className="h-4 w-4" />
          </button>

          <button
            onClick={handleAnalyze}
            disabled={isAnalyzing}
            className={cn(
              "flex items-center gap-2 px-4 py-2 bg-primary text-primary-foreground rounded-md font-medium transition-colors",
              isAnalyzing 
                ? "opacity-50 cursor-not-allowed" 
                : "hover:bg-primary/90"
            )}
          >
            {isAnalyzing ? (
              <>
                <RefreshCw className="h-4 w-4 animate-spin" />
                Анализ...
              </>
            ) : (
              <>
                <Zap className="h-4 w-4" />
                Анализировать
              </>
            )}
          </button>
        </div>
      </div>

      {/* Main Grid Layout - Desktop ≥1280px */}
      <div className="grid grid-cols-12 gap-6 h-[calc(100vh-200px)]">
        {/* Chart Widget - 9 columns */}
        <div className="col-span-9">
          <ChartWidget />
        </div>

        {/* Signal Cards - 3 columns */}
        <div className="col-span-3">
          <SignalCards />
        </div>
      </div>

      {/* News Feed - Full width */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">Новости рынка</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {[1, 2, 3].map((i) => (
            <div key={i} className="p-4 bg-card rounded-lg border space-y-2">
              <div className="flex items-center gap-2 text-xs text-muted-foreground">
                <span>CoinDesk</span>
                <span>•</span>
                <span>2 часа назад</span>
              </div>
              <h4 className="font-medium line-clamp-2">
                Bitcoin достигает нового максимума на фоне институционального интереса
              </h4>
              <p className="text-sm text-muted-foreground line-clamp-3">
                Цена Bitcoin продолжает расти благодаря увеличению интереса со стороны 
                институциональных инвесторов и позитивным новостям о регулировании...
              </p>
              <div className="flex items-center gap-2">
                <TrendingUp className="h-4 w-4 text-success" />
                <span className="text-sm text-success">Бычьи настроения</span>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Recent Reports - Full width */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">Последние отчеты</h3>
        <div className="space-y-3">
          {[1, 2, 3].map((i) => (
            <div key={i} className="p-4 bg-card rounded-lg border">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-primary/10 rounded-lg">
                    <BarChart3 className="h-5 w-5 text-primary" />
                  </div>
                  <div>
                    <h4 className="font-medium">Анализ BTCUSDT - 4h</h4>
                    <p className="text-sm text-muted-foreground">
                      Технический анализ с рекомендациями по входу
                    </p>
                  </div>
                </div>
                
                <div className="flex items-center gap-4">
                  <div className="text-right">
                    <div className="text-sm font-medium text-success">LONG</div>
                    <div className="text-xs text-muted-foreground">Уверенность: 87%</div>
                  </div>
                  
                  <button className="flex items-center gap-2 px-3 py-2 bg-accent hover:bg-accent/80 rounded-md text-sm transition-colors">
                    <Play className="h-4 w-4" />
                    Открыть
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}
