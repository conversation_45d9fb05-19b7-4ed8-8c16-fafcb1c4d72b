import React, { useState } from 'react'
import { Check, Crown, Zap, Star, CreditCard } from 'lucide-react'

import { useAuthStore } from '@/stores/authStore'
import { useTelegramWebApp } from '@/hooks/useTelegramWebApp'
import { useDeviceDetection } from '@/hooks/useDeviceDetection'
import { cn } from '@/lib/utils'

const plans = [
  {
    id: 'free',
    name: 'Базовый',
    price: 0,
    period: 'навсегда',
    description: 'Для знакомства с платформой',
    icon: Star,
    features: [
      'Базовый анализ криптовалют',
      'Просмотр публичных сигналов',
      'Доступ к новостям рынка',
      'Техническая поддержка',
    ],
    limitations: [
      'Ограниченное количество анализов',
      'Нет персональных сигналов',
      'Нет доступа к премиум индикаторам',
    ],
    popular: false,
  },
  {
    id: 'premium',
    name: 'Премиум',
    price: 29,
    period: 'месяц',
    description: 'Для активных трейдеров',
    icon: Crown,
    features: [
      'Неограниченный анализ',
      'Персональные торговые сигналы',
      'Все технические индикаторы',
      'Уведомления в реальном времени',
      'Экспорт отчетов',
      'Приоритетная поддержка',
    ],
    limitations: [],
    popular: true,
  },
  {
    id: 'vip',
    name: 'VIP',
    price: 79,
    period: 'месяц',
    description: 'Для профессиональных трейдеров',
    icon: Zap,
    features: [
      'Все функции Премиум',
      'Персональный аналитик',
      'Эксклюзивные стратегии',
      'Доступ к закрытому сообществу',
      'Индивидуальные консультации',
      'API доступ',
      'Белый лейбл решения',
    ],
    limitations: [],
    popular: false,
  },
]

export default function PlansPage() {
  const { user } = useAuthStore()
  const { isWebApp, showMainButton, hideMainButton, hapticFeedback } = useTelegramWebApp()
  const { isDesktop } = useDeviceDetection()
  const [selectedPlan, setSelectedPlan] = useState<string | null>(null)
  const [isProcessing, setIsProcessing] = useState(false)

  const handleSelectPlan = (planId: string) => {
    setSelectedPlan(planId)
    
    if (isWebApp) {
      hapticFeedback('impact', 'light')
      
      if (planId !== 'free') {
        showMainButton(`Оплатить ${plans.find(p => p.id === planId)?.name}`, () => {
          handlePurchase(planId)
        })
      } else {
        hideMainButton()
      }
    }
  }

  const handlePurchase = async (planId: string) => {
    if (planId === 'free') return

    setIsProcessing(true)
    
    try {
      if (isWebApp) {
        hapticFeedback('notification', 'success')
        // TODO: Implement Telegram Payments
        console.log('Telegram Payments for plan:', planId)
      } else {
        // TODO: Implement Stripe Checkout
        console.log('Stripe Checkout for plan:', planId)
      }
    } catch (error) {
      console.error('Payment failed:', error)
      if (isWebApp) {
        hapticFeedback('notification', 'error')
      }
    } finally {
      setIsProcessing(false)
    }
  }

  const currentPlan = user?.tier || 'free'

  return (
    <div className={cn(
      "space-y-8",
      isDesktop ? "p-0" : "p-4",
      isWebApp && "pb-safe"
    )}>
      {/* Header */}
      <div className="text-center space-y-4">
        <h1 className="text-3xl font-bold">Тарифные планы</h1>
        <p className="text-muted-foreground max-w-2xl mx-auto">
          Выберите подходящий тариф для максимальной эффективности торговли. 
          Все планы включают доступ к базовым функциям анализа.
        </p>
        
        {user && (
          <div className="inline-flex items-center gap-2 px-4 py-2 bg-primary/10 text-primary rounded-full text-sm">
            <Crown className="h-4 w-4" />
            Текущий план: <span className="font-medium capitalize">{currentPlan}</span>
          </div>
        )}
      </div>

      {/* Plans Grid */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-6xl mx-auto">
        {plans.map((plan) => {
          const Icon = plan.icon
          const isCurrentPlan = currentPlan === plan.id
          const isSelected = selectedPlan === plan.id
          
          return (
            <div
              key={plan.id}
              onClick={() => handleSelectPlan(plan.id)}
              className={cn(
                "relative p-6 bg-card rounded-xl border-2 transition-all cursor-pointer",
                plan.popular && "border-primary shadow-lg scale-105",
                isSelected && "border-primary",
                isCurrentPlan && "bg-primary/5 border-primary",
                !plan.popular && !isSelected && !isCurrentPlan && "border-border hover:border-primary/50"
              )}
            >
              {/* Popular Badge */}
              {plan.popular && (
                <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                  <div className="px-4 py-1 bg-primary text-primary-foreground text-xs font-medium rounded-full">
                    Популярный
                  </div>
                </div>
              )}

              {/* Current Plan Badge */}
              {isCurrentPlan && (
                <div className="absolute -top-3 right-4">
                  <div className="px-3 py-1 bg-success text-success-foreground text-xs font-medium rounded-full">
                    Активен
                  </div>
                </div>
              )}

              {/* Plan Header */}
              <div className="text-center space-y-4">
                <div className={cn(
                  "inline-flex items-center justify-center w-16 h-16 rounded-full",
                  plan.popular ? "bg-primary/10 text-primary" : "bg-muted text-muted-foreground"
                )}>
                  <Icon className="h-8 w-8" />
                </div>

                <div>
                  <h3 className="text-xl font-bold">{plan.name}</h3>
                  <p className="text-sm text-muted-foreground">{plan.description}</p>
                </div>

                <div className="space-y-1">
                  <div className="flex items-baseline justify-center gap-1">
                    <span className="text-3xl font-bold">
                      {plan.price === 0 ? 'Бесплатно' : `$${plan.price}`}
                    </span>
                    {plan.price > 0 && (
                      <span className="text-muted-foreground">/{plan.period}</span>
                    )}
                  </div>
                  {plan.price > 0 && (
                    <p className="text-xs text-muted-foreground">
                      ≈ {Math.round(plan.price * 90)} ₽/месяц
                    </p>
                  )}
                </div>
              </div>

              {/* Features */}
              <div className="space-y-4 mt-6">
                <div className="space-y-3">
                  {plan.features.map((feature, index) => (
                    <div key={index} className="flex items-center gap-3">
                      <Check className="h-4 w-4 text-success flex-shrink-0" />
                      <span className="text-sm">{feature}</span>
                    </div>
                  ))}
                </div>

                {plan.limitations.length > 0 && (
                  <div className="pt-3 border-t space-y-2">
                    <p className="text-xs text-muted-foreground font-medium">Ограничения:</p>
                    {plan.limitations.map((limitation, index) => (
                      <div key={index} className="flex items-center gap-3">
                        <div className="w-4 h-4 flex-shrink-0 flex items-center justify-center">
                          <div className="w-1 h-1 bg-muted-foreground rounded-full"></div>
                        </div>
                        <span className="text-xs text-muted-foreground">{limitation}</span>
                      </div>
                    ))}
                  </div>
                )}
              </div>

              {/* Action Button */}
              <div className="mt-6">
                {isCurrentPlan ? (
                  <div className="w-full py-3 bg-success/10 text-success text-center rounded-lg font-medium">
                    Текущий план
                  </div>
                ) : plan.id === 'free' ? (
                  <div className="w-full py-3 bg-muted text-muted-foreground text-center rounded-lg font-medium">
                    Базовый доступ
                  </div>
                ) : !isWebApp ? (
                  <button
                    onClick={(e) => {
                      e.stopPropagation()
                      handlePurchase(plan.id)
                    }}
                    disabled={isProcessing}
                    className={cn(
                      "w-full flex items-center justify-center gap-2 py-3 rounded-lg font-medium transition-colors",
                      plan.popular 
                        ? "bg-primary text-primary-foreground hover:bg-primary/90" 
                        : "bg-secondary text-secondary-foreground hover:bg-secondary/90",
                      isProcessing && "opacity-50 cursor-not-allowed"
                    )}
                  >
                    <CreditCard className="h-4 w-4" />
                    {isProcessing ? 'Обработка...' : 'Выбрать план'}
                  </button>
                ) : (
                  <div className="w-full py-3 bg-primary/10 text-primary text-center rounded-lg font-medium">
                    Выбрано
                  </div>
                )}
              </div>
            </div>
          )
        })}
      </div>

      {/* FAQ */}
      <div className="max-w-3xl mx-auto space-y-6">
        <h2 className="text-2xl font-bold text-center">Часто задаваемые вопросы</h2>
        
        <div className="space-y-4">
          <div className="p-4 bg-card rounded-lg border">
            <h3 className="font-medium mb-2">Можно ли изменить план в любое время?</h3>
            <p className="text-sm text-muted-foreground">
              Да, вы можете повысить или понизить тариф в любое время. 
              При повышении тарифа доплачивается разница, при понижении - 
              остаток переносится на следующий период.
            </p>
          </div>

          <div className="p-4 bg-card rounded-lg border">
            <h3 className="font-medium mb-2">Есть ли гарантия возврата средств?</h3>
            <p className="text-sm text-muted-foreground">
              Мы предоставляем 7-дневную гарантию возврата средств для всех платных планов. 
              Если сервис вам не подойдет, мы вернем полную стоимость.
            </p>
          </div>

          <div className="p-4 bg-card rounded-lg border">
            <h3 className="font-medium mb-2">Какие способы оплаты доступны?</h3>
            <p className="text-sm text-muted-foreground">
              {isWebApp 
                ? 'В Telegram Mini App доступна оплата через Telegram Payments (карты, Apple Pay, Google Pay).'
                : 'Доступна оплата банковскими картами через Stripe, а также Apple Pay и Google Pay.'
              }
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}
