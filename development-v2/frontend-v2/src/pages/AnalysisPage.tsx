import React from 'react'
import { BarChart3, TrendingUp, Clock } from 'lucide-react'

export default function AnalysisPage() {
  return (
    <div className="p-4 space-y-6">
      <div className="text-center space-y-4">
        <div className="flex justify-center">
          <div className="p-4 bg-primary/10 rounded-full">
            <BarChart3 className="h-12 w-12 text-primary" />
          </div>
        </div>
        
        <div>
          <h1 className="text-2xl font-bold">Анализ криптовалют</h1>
          <p className="text-muted-foreground">
            Мобильная версия анализа (в разработке)
          </p>
        </div>
      </div>

      <div className="space-y-4">
        <div className="p-4 bg-card rounded-lg border">
          <div className="flex items-center gap-3 mb-3">
            <TrendingUp className="h-5 w-5 text-primary" />
            <h3 className="font-medium">Быстрый анализ</h3>
          </div>
          <p className="text-sm text-muted-foreground">
            Функция быстрого анализа будет доступна в следующих обновлениях
          </p>
        </div>

        <div className="p-4 bg-card rounded-lg border">
          <div className="flex items-center gap-3 mb-3">
            <Clock className="h-5 w-5 text-primary" />
            <h3 className="font-medium">История анализов</h3>
          </div>
          <p className="text-sm text-muted-foreground">
            Здесь будет отображаться история ваших анализов
          </p>
        </div>
      </div>
    </div>
  )
}
