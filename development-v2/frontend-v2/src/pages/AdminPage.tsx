import React from 'react'
import { Shield, Users, MessageSquare, Settings } from 'lucide-react'

export default function AdminPage() {
  return (
    <div className="p-4 space-y-6">
      <div className="text-center space-y-4">
        <div className="flex justify-center">
          <div className="p-4 bg-primary/10 rounded-full">
            <Shield className="h-12 w-12 text-primary" />
          </div>
        </div>
        
        <div>
          <h1 className="text-2xl font-bold">Панель администратора</h1>
          <p className="text-muted-foreground">
            Управление пользователями и системой
          </p>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="p-4 bg-card rounded-lg border">
          <div className="flex items-center gap-3 mb-3">
            <Users className="h-5 w-5 text-primary" />
            <h3 className="font-medium">Пользователи</h3>
          </div>
          <p className="text-sm text-muted-foreground">
            Управление пользователями и их правами
          </p>
        </div>

        <div className="p-4 bg-card rounded-lg border">
          <div className="flex items-center gap-3 mb-3">
            <MessageSquare className="h-5 w-5 text-primary" />
            <h3 className="font-medium">Промпты</h3>
          </div>
          <p className="text-sm text-muted-foreground">
            Управление промптами для LLM
          </p>
        </div>

        <div className="p-4 bg-card rounded-lg border">
          <div className="flex items-center gap-3 mb-3">
            <Settings className="h-5 w-5 text-primary" />
            <h3 className="font-medium">LLM Router</h3>
          </div>
          <p className="text-sm text-muted-foreground">
            Переключение между OpenAI и Gemini
          </p>
        </div>

        <div className="p-4 bg-card rounded-lg border">
          <div className="flex items-center gap-3 mb-3">
            <MessageSquare className="h-5 w-5 text-primary" />
            <h3 className="font-medium">Рассылка</h3>
          </div>
          <p className="text-sm text-muted-foreground">
            Массовая рассылка сообщений
          </p>
        </div>
      </div>
    </div>
  )
}
