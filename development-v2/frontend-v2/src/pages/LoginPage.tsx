import React, { useState } from 'react'
import { Navigate } from 'react-router-dom'
import { BarChart3, LogIn, Loader2 } from 'lucide-react'

import { useAuthStore } from '@/stores/authStore'
import { useTelegramAuth } from '@/hooks/useTelegramWebApp'
import { useDeviceDetection } from '@/hooks/useDeviceDetection'
import { cn } from '@/lib/utils'

export default function LoginPage() {
  const { login, isLoading, error, isAuthenticated } = useAuthStore()
  const { isWebApp, canAuthenticate, getTelegramAuthData } = useTelegramAuth()
  const { isDesktop } = useDeviceDetection()
  const [isLoggingIn, setIsLoggingIn] = useState(false)

  // Redirect if already authenticated
  if (isAuthenticated) {
    return <Navigate to="/" replace />
  }

  const handleTelegramLogin = async () => {
    if (!canAuthenticate) return

    try {
      setIsLoggingIn(true)
      const authData = getTelegramAuthData()
      
      if (authData) {
        await login(authData)
      }
    } catch (error) {
      console.error('Telegram login failed:', error)
    } finally {
      setIsLoggingIn(false)
    }
  }

  const handleDemoLogin = async () => {
    try {
      setIsLoggingIn(true)
      // Demo login for desktop version
      await login({
        telegram_id: '299820674', // Admin ID from TZ
        username: 'demo_user',
      })
    } catch (error) {
      console.error('Demo login failed:', error)
    } finally {
      setIsLoggingIn(false)
    }
  }

  return (
    <div className={cn(
      "min-h-screen flex items-center justify-center bg-gradient-to-br from-background to-muted/20",
      isWebApp && "twa-viewport"
    )}>
      <div className="w-full max-w-md p-6 space-y-8">
        {/* Logo and Title */}
        <div className="text-center space-y-4">
          <div className="flex justify-center">
            <div className="p-4 bg-primary/10 rounded-full">
              <BarChart3 className="h-12 w-12 text-primary" />
            </div>
          </div>
          
          <div className="space-y-2">
            <h1 className="text-3xl font-bold">ChartGenius</h1>
            <p className="text-muted-foreground">
              {isWebApp 
                ? 'Добро пожаловать в Telegram Mini App'
                : 'Профессиональная аналитика криптовалют'
              }
            </p>
            <div className="text-xs text-muted-foreground">
              v2.0.0-dev • Powered by AI
            </div>
          </div>
        </div>

        {/* Login Form */}
        <div className="space-y-4">
          {/* Telegram WebApp Login */}
          {isWebApp && canAuthenticate && (
            <button
              onClick={handleTelegramLogin}
              disabled={isLoading || isLoggingIn}
              className="w-full flex items-center justify-center gap-3 px-4 py-3 bg-primary text-primary-foreground rounded-lg font-medium hover:bg-primary/90 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              {isLoggingIn ? (
                <Loader2 className="h-5 w-5 animate-spin" />
              ) : (
                <LogIn className="h-5 w-5" />
              )}
              Войти через Telegram
            </button>
          )}

          {/* Desktop Demo Login */}
          {!isWebApp && isDesktop && (
            <button
              onClick={handleDemoLogin}
              disabled={isLoading || isLoggingIn}
              className="w-full flex items-center justify-center gap-3 px-4 py-3 bg-secondary text-secondary-foreground rounded-lg font-medium hover:bg-secondary/90 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              {isLoggingIn ? (
                <Loader2 className="h-5 w-5 animate-spin" />
              ) : (
                <BarChart3 className="h-5 w-5" />
              )}
              Демо-версия
            </button>
          )}

          {/* Error Message */}
          {error && (
            <div className="p-3 bg-destructive/10 border border-destructive/20 rounded-lg">
              <p className="text-sm text-destructive text-center">{error}</p>
            </div>
          )}

          {/* WebApp Not Available */}
          {!isWebApp && !isDesktop && (
            <div className="p-4 bg-muted/50 rounded-lg text-center space-y-2">
              <p className="text-sm text-muted-foreground">
                Для мобильных устройств используйте Telegram Mini App
              </p>
              <p className="text-xs text-muted-foreground">
                Откройте @Chart_Genius_bot в Telegram
              </p>
            </div>
          )}
        </div>

        {/* Features */}
        <div className="space-y-4">
          <div className="text-center">
            <h3 className="text-sm font-medium text-muted-foreground mb-3">
              Возможности платформы
            </h3>
          </div>
          
          <div className="grid grid-cols-1 gap-3">
            <div className="flex items-center gap-3 p-3 bg-card rounded-lg border">
              <div className="w-2 h-2 bg-primary rounded-full"></div>
              <span className="text-sm">AI-анализ криптовалют</span>
            </div>
            
            <div className="flex items-center gap-3 p-3 bg-card rounded-lg border">
              <div className="w-2 h-2 bg-primary rounded-full"></div>
              <span className="text-sm">Торговые сигналы в реальном времени</span>
            </div>
            
            <div className="flex items-center gap-3 p-3 bg-card rounded-lg border">
              <div className="w-2 h-2 bg-primary rounded-full"></div>
              <span className="text-sm">Технический анализ с индикаторами</span>
            </div>
            
            {isDesktop && (
              <div className="flex items-center gap-3 p-3 bg-card rounded-lg border">
                <div className="w-2 h-2 bg-primary rounded-full"></div>
                <span className="text-sm">Полнофункциональный десктоп интерфейс</span>
              </div>
            )}
          </div>
        </div>

        {/* Footer */}
        <div className="text-center text-xs text-muted-foreground space-y-1">
          <p>© 2025 ChartGenius. Все права защищены.</p>
          {isWebApp && (
            <p>Работает в Telegram Mini App</p>
          )}
        </div>
      </div>
    </div>
  )
}
