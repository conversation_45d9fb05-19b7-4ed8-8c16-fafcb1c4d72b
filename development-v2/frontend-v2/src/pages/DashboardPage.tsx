import React from 'react'
import { <PERSON> } from 'react-router-dom'
import { 
  TrendingUp, 
  TrendingDown, 
  BarChart3, 
  DollarSign,
  Clock,
  Star,
  ArrowRight,
  Crown
} from 'lucide-react'

import { useAuthStore } from '@/stores/authStore'
import { useDeviceDetection } from '@/hooks/useDeviceDetection'
import { useTelegramWebApp } from '@/hooks/useTelegramWebApp'
import { formatCurrency, formatPercentage, formatRelativeTime } from '@/lib/utils'
import { cn } from '@/lib/utils'

// Mock data for demonstration
const mockSignals = [
  {
    id: '1',
    symbol: 'BTCUSDT',
    signal: 'long' as const,
    price: 67420.50,
    change: 2.34,
    confidence: 85,
    timeframe: '4h',
    created_at: new Date(Date.now() - 1000 * 60 * 30), // 30 minutes ago
  },
  {
    id: '2',
    symbol: 'ETHUSDT',
    signal: 'short' as const,
    price: 3245.80,
    change: -1.67,
    confidence: 78,
    timeframe: '1h',
    created_at: new Date(Date.now() - 1000 * 60 * 60 * 2), // 2 hours ago
  },
  {
    id: '3',
    symbol: 'ADAUSDT',
    signal: 'long' as const,
    price: 0.4521,
    change: 5.23,
    confidence: 92,
    timeframe: '1d',
    created_at: new Date(Date.now() - 1000 * 60 * 60 * 4), // 4 hours ago
  },
]

const mockStats = {
  totalSignals: 156,
  successRate: 73.2,
  avgReturn: 4.8,
  activeWatchlist: 12,
}

export default function DashboardPage() {
  const { user } = useAuthStore()
  const { isDesktop, isMobile } = useDeviceDetection()
  const { isWebApp, hapticFeedback } = useTelegramWebApp()

  const handleSignalClick = (signal: typeof mockSignals[0]) => {
    if (isWebApp) {
      hapticFeedback('impact', 'light')
    }
    // Navigate to signal details or analysis
  }

  const canAccessSignals = user?.role === 'premium' || user?.role === 'vip' || user?.role === 'admin'

  return (
    <div className={cn(
      "space-y-6",
      isDesktop ? "p-0" : "p-4",
      isWebApp && "pb-safe"
    )}>
      {/* Welcome Section */}
      <div className="space-y-4">
        <div>
          <h1 className="text-2xl font-bold">
            Добро пожаловать{user?.first_name ? `, ${user.first_name}` : ''}!
          </h1>
          <p className="text-muted-foreground">
            {isWebApp 
              ? 'Ваш персональный помощник для торговли криптовалютами'
              : 'Обзор рынка и ваших торговых сигналов'
            }
          </p>
        </div>

        {/* User Status */}
        {user && (
          <div className="flex items-center gap-4 p-4 bg-card rounded-lg border">
            <div className="flex items-center justify-center w-12 h-12 bg-primary/10 rounded-full">
              <Crown className="h-6 w-6 text-primary" />
            </div>
            <div className="flex-1">
              <div className="flex items-center gap-2">
                <span className="font-medium capitalize">{user.tier}</span>
                {user.tier !== 'free' && (
                  <div className="w-2 h-2 bg-primary rounded-full"></div>
                )}
              </div>
              <p className="text-sm text-muted-foreground">
                {user.tier === 'free' 
                  ? 'Базовый доступ к анализу'
                  : user.tier === 'premium'
                  ? 'Доступ к сигналам и анализу'
                  : 'Полный доступ ко всем функциям'
                }
              </p>
            </div>
            {user.tier === 'free' && (
              <Link
                to="/plans"
                className="px-4 py-2 bg-primary text-primary-foreground rounded-md text-sm font-medium hover:bg-primary/90 transition-colors"
              >
                Улучшить
              </Link>
            )}
          </div>
        )}
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
        <div className="p-4 bg-card rounded-lg border space-y-2">
          <div className="flex items-center gap-2">
            <BarChart3 className="h-4 w-4 text-muted-foreground" />
            <span className="text-sm text-muted-foreground">Сигналы</span>
          </div>
          <div className="text-2xl font-bold">{mockStats.totalSignals}</div>
        </div>

        <div className="p-4 bg-card rounded-lg border space-y-2">
          <div className="flex items-center gap-2">
            <TrendingUp className="h-4 w-4 text-success" />
            <span className="text-sm text-muted-foreground">Точность</span>
          </div>
          <div className="text-2xl font-bold text-success">
            {formatPercentage(mockStats.successRate, 1, false)}
          </div>
        </div>

        <div className="p-4 bg-card rounded-lg border space-y-2">
          <div className="flex items-center gap-2">
            <DollarSign className="h-4 w-4 text-primary" />
            <span className="text-sm text-muted-foreground">Доходность</span>
          </div>
          <div className="text-2xl font-bold text-primary">
            {formatPercentage(mockStats.avgReturn, 1)}
          </div>
        </div>

        <div className="p-4 bg-card rounded-lg border space-y-2">
          <div className="flex items-center gap-2">
            <Star className="h-4 w-4 text-warning" />
            <span className="text-sm text-muted-foreground">Избранное</span>
          </div>
          <div className="text-2xl font-bold">{mockStats.activeWatchlist}</div>
        </div>
      </div>

      {/* Recent Signals */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h2 className="text-lg font-semibold">Последние сигналы</h2>
          {canAccessSignals && (
            <Link
              to="/analysis"
              className="flex items-center gap-1 text-sm text-primary hover:text-primary/80 transition-colors"
            >
              Все сигналы
              <ArrowRight className="h-4 w-4" />
            </Link>
          )}
        </div>

        {canAccessSignals ? (
          <div className="space-y-3">
            {mockSignals.map((signal) => (
              <div
                key={signal.id}
                onClick={() => handleSignalClick(signal)}
                className="p-4 bg-card rounded-lg border hover:bg-accent/50 transition-colors cursor-pointer"
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className={cn(
                      "flex items-center justify-center w-10 h-10 rounded-full",
                      signal.signal === 'long' 
                        ? "bg-success/10 text-success" 
                        : "bg-destructive/10 text-destructive"
                    )}>
                      {signal.signal === 'long' ? (
                        <TrendingUp className="h-5 w-5" />
                      ) : (
                        <TrendingDown className="h-5 w-5" />
                      )}
                    </div>
                    
                    <div>
                      <div className="flex items-center gap-2">
                        <span className="font-medium">{signal.symbol}</span>
                        <span className="text-xs bg-muted px-2 py-1 rounded">
                          {signal.timeframe}
                        </span>
                      </div>
                      <div className="flex items-center gap-2 text-sm text-muted-foreground">
                        <Clock className="h-3 w-3" />
                        {formatRelativeTime(signal.created_at)}
                      </div>
                    </div>
                  </div>

                  <div className="text-right">
                    <div className="font-medium">
                      {formatCurrency(signal.price, 'USD')}
                    </div>
                    <div className={cn(
                      "text-sm",
                      signal.change > 0 ? "text-success" : "text-destructive"
                    )}>
                      {formatPercentage(signal.change)}
                    </div>
                  </div>
                </div>

                <div className="mt-3 flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <span className="text-xs text-muted-foreground">Уверенность:</span>
                    <div className="flex items-center gap-1">
                      <div className="w-16 h-2 bg-muted rounded-full overflow-hidden">
                        <div 
                          className="h-full bg-primary rounded-full transition-all"
                          style={{ width: `${signal.confidence}%` }}
                        />
                      </div>
                      <span className="text-xs font-medium">{signal.confidence}%</span>
                    </div>
                  </div>

                  <div className={cn(
                    "px-2 py-1 rounded text-xs font-medium uppercase",
                    signal.signal === 'long' 
                      ? "bg-success/10 text-success" 
                      : "bg-destructive/10 text-destructive"
                  )}>
                    {signal.signal}
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="p-6 bg-card rounded-lg border text-center space-y-3">
            <Crown className="h-12 w-12 text-muted-foreground mx-auto" />
            <div>
              <h3 className="font-medium">Премиум функция</h3>
              <p className="text-sm text-muted-foreground">
                Торговые сигналы доступны в тарифах Premium и VIP
              </p>
            </div>
            <Link
              to="/plans"
              className="inline-flex items-center gap-2 px-4 py-2 bg-primary text-primary-foreground rounded-md text-sm font-medium hover:bg-primary/90 transition-colors"
            >
              <Crown className="h-4 w-4" />
              Выбрать тариф
            </Link>
          </div>
        )}
      </div>

      {/* Quick Actions */}
      <div className="space-y-4">
        <h2 className="text-lg font-semibold">Быстрые действия</h2>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <Link
            to={isDesktop ? "/desktop" : "/analysis"}
            className="p-4 bg-card rounded-lg border hover:bg-accent/50 transition-colors group"
          >
            <div className="flex items-center gap-3">
              <div className="p-2 bg-primary/10 rounded-lg group-hover:bg-primary/20 transition-colors">
                <BarChart3 className="h-5 w-5 text-primary" />
              </div>
              <div>
                <h3 className="font-medium">Создать анализ</h3>
                <p className="text-sm text-muted-foreground">
                  Анализ криптовалюты с помощью ИИ
                </p>
              </div>
            </div>
          </Link>

          <Link
            to="/plans"
            className="p-4 bg-card rounded-lg border hover:bg-accent/50 transition-colors group"
          >
            <div className="flex items-center gap-3">
              <div className="p-2 bg-warning/10 rounded-lg group-hover:bg-warning/20 transition-colors">
                <Crown className="h-5 w-5 text-warning" />
              </div>
              <div>
                <h3 className="font-medium">Тарифные планы</h3>
                <p className="text-sm text-muted-foreground">
                  Выберите подходящий тариф
                </p>
              </div>
            </div>
          </Link>
        </div>
      </div>
    </div>
  )
}
