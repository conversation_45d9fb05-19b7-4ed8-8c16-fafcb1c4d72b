import { create } from 'zustand'
import { persist, createJSONStorage } from 'zustand/middleware'
import type { User, AuthState, LoginRequest, LoginResponse } from '@/types/auth'

interface AuthStore extends AuthState {
  // Actions
  setUser: (user: User | null) => void
  setToken: (token: string | null) => void
  setLoading: (loading: boolean) => void
  setError: (error: string | null) => void
  
  // Auth methods
  login: (credentials: LoginRequest) => Promise<void>
  logout: () => void
  refreshToken: () => Promise<void>
  initializeAuth: () => Promise<void>
  
  // Utility methods
  clearError: () => void
  updateUser: (updates: Partial<User>) => void
}

const API_BASE_URL = `${import.meta.env.VITE_API_BASE_URL || 'http://localhost:8001'}/api/v2`

export const useAuthStore = create<AuthStore>()(
  persist(
    (set, get) => ({
      // Initial state
      user: null,
      token: null,
      isAuthenticated: false,
      isLoading: false,
      error: null,

      // Setters
      setUser: (user) => {
        set({ 
          user, 
          isAuthenticated: !!user,
          error: null 
        })
      },

      setToken: (token) => {
        set({ 
          token,
          isAuthenticated: !!token && !!get().user,
          error: null 
        })
      },

      setLoading: (isLoading) => {
        set({ isLoading })
      },

      setError: (error) => {
        set({ error, isLoading: false })
      },

      clearError: () => {
        set({ error: null })
      },

      updateUser: (updates) => {
        const currentUser = get().user
        if (currentUser) {
          set({ 
            user: { ...currentUser, ...updates },
            error: null 
          })
        }
      },

      // Auth methods
      login: async (credentials) => {
        const { setLoading, setError, setUser, setToken } = get()
        
        try {
          setLoading(true)
          setError(null)

          const response = await fetch(`${API_BASE_URL}/auth/login`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify(credentials),
          })

          if (!response.ok) {
            const errorData = await response.json().catch(() => ({}))
            throw new Error(errorData.detail || 'Ошибка входа')
          }

          const data: LoginResponse = await response.json()
          
          setToken(data.token)
          setUser(data.user)
          
          console.log('Успешный вход:', data.user.telegram_id)
        } catch (error) {
          const message = error instanceof Error ? error.message : 'Неизвестная ошибка'
          setError(message)
          throw error
        } finally {
          setLoading(false)
        }
      },

      logout: () => {
        set({
          user: null,
          token: null,
          isAuthenticated: false,
          error: null,
          isLoading: false,
        })
        
        // Clear any cached data
        localStorage.removeItem('chartgenius-auth-storage')
        
        console.log('Пользователь вышел из системы')
      },

      refreshToken: async () => {
        const { token, setToken, setUser, logout } = get()
        
        if (!token) {
          logout()
          return
        }

        try {
          const response = await fetch(`${API_BASE_URL}/auth/refresh`, {
            method: 'POST',
            headers: {
              'Authorization': `Bearer ${token}`,
              'Content-Type': 'application/json',
            },
          })

          if (!response.ok) {
            throw new Error('Не удалось обновить токен')
          }

          const data = await response.json()
          setToken(data.token)
          
          // Optionally fetch updated user profile
          if (data.user) {
            setUser(data.user)
          }
          
          console.log('Токен успешно обновлен')
        } catch (error) {
          console.error('Ошибка обновления токена:', error)
          logout()
        }
      },

      initializeAuth: async () => {
        const { token, setLoading, setUser, setToken, logout } = get()

        // For local development, create a mock token if none exists
        if (!token && import.meta.env.VITE_ENVIRONMENT === 'local') {
          const mockToken = 'mock_jwt_token_local_dev_' + Date.now()
          const mockUser: User = {
            id: '1',
            telegram_id: '299820674',
            username: 'test_user',
            first_name: 'Test',
            last_name: 'User',
            role: 'admin',
            tier: 'premium',
            subscription: {
              id: '1',
              user_id: '1',
              tier: 'premium',
              price: 0,
              exp_ts: Math.floor(new Date('2025-12-31').getTime() / 1000),
              source: 'telegram',
              status: 'active',
              created_at: '2024-01-01T00:00:00Z',
              updated_at: new Date().toISOString()
            },
            created_at: '2024-01-01T00:00:00Z',
            updated_at: new Date().toISOString()
          }

          setToken(mockToken)
          setUser(mockUser)
          setLoading(false)
          console.log('🔧 Local development: Mock authentication initialized')
          return
        }

        if (!token) {
          setLoading(false)
          return
        }

        try {
          setLoading(true)

          // Verify token and get user profile
          const response = await fetch(`${API_BASE_URL}/auth/profile`, {
            headers: {
              'Authorization': `Bearer ${token}`,
            },
          })

          if (!response.ok) {
            throw new Error('Токен недействителен')
          }

          const user: User = await response.json()
          setUser(user)

          console.log('Аутентификация инициализирована:', user.telegram_id)
        } catch (error) {
          console.error('Ошибка инициализации аутентификации:', error)
          logout()
        } finally {
          setLoading(false)
        }
      },
    }),
    {
      name: 'chartgenius-auth-storage',
      storage: createJSONStorage(() => localStorage),
      partialize: (state) => ({
        token: state.token,
        user: state.user,
      }),
      onRehydrateStorage: () => (state) => {
        if (state) {
          // Update authentication status based on persisted data
          state.isAuthenticated = !!(state.token && state.user)
          state.isLoading = false
          state.error = null
        }
      },
    }
  )
)

// Helper function to check if token is expired
export const isTokenExpired = (token: string): boolean => {
  try {
    const payload = JSON.parse(atob(token.split('.')[1]))
    const currentTime = Math.floor(Date.now() / 1000)
    return payload.exp < currentTime
  } catch {
    return true
  }
}

// Helper function to check if token expires soon (within 5 minutes)
export const isTokenExpiringSoon = (token: string): boolean => {
  try {
    const payload = JSON.parse(atob(token.split('.')[1]))
    const currentTime = Math.floor(Date.now() / 1000)
    const fiveMinutes = 5 * 60
    return (payload.exp - currentTime) < fiveMinutes
  } catch {
    return true
  }
}

// Auto-refresh token when it's about to expire
let refreshInterval: NodeJS.Timeout | null = null

export const startTokenRefreshInterval = () => {
  if (refreshInterval) {
    clearInterval(refreshInterval)
  }

  refreshInterval = setInterval(() => {
    const { token, refreshToken, isAuthenticated } = useAuthStore.getState()
    
    if (isAuthenticated && token && isTokenExpiringSoon(token)) {
      console.log('Токен скоро истечет, обновляем...')
      refreshToken()
    }
  }, 60000) // Check every minute
}

export const stopTokenRefreshInterval = () => {
  if (refreshInterval) {
    clearInterval(refreshInterval)
    refreshInterval = null
  }
}
