import '@testing-library/jest-dom'

// Mock Telegram WebApp
Object.defineProperty(window, 'Telegram', {
  value: {
    WebApp: {
      initData: '',
      initDataUnsafe: {},
      version: '6.0',
      platform: 'web',
      colorScheme: 'dark',
      themeParams: {},
      isExpanded: false,
      viewportHeight: 600,
      viewportStableHeight: 600,
      headerColor: '#000000',
      backgroundColor: '#000000',
      isClosingConfirmationEnabled: false,
      
      ready: jest.fn(),
      expand: jest.fn(),
      close: jest.fn(),
      enableClosingConfirmation: jest.fn(),
      disableClosingConfirmation: jest.fn(),
      showPopup: jest.fn(),
      showAlert: jest.fn(),
      showConfirm: jest.fn(),
      showScanQrPopup: jest.fn(),
      closeScanQrPopup: jest.fn(),
      readTextFromClipboard: jest.fn(),
      requestWriteAccess: jest.fn(),
      requestContact: jest.fn(),
      
      MainButton: {
        text: '',
        color: '#000000',
        textColor: '#ffffff',
        isVisible: false,
        isActive: true,
        isProgressVisible: false,
        setText: jest.fn(),
        onClick: jest.fn(),
        offClick: jest.fn(),
        show: jest.fn(),
        hide: jest.fn(),
        enable: jest.fn(),
        disable: jest.fn(),
        showProgress: jest.fn(),
        hideProgress: jest.fn(),
        setParams: jest.fn(),
      },
      
      BackButton: {
        isVisible: false,
        onClick: jest.fn(),
        offClick: jest.fn(),
        show: jest.fn(),
        hide: jest.fn(),
      },
      
      HapticFeedback: {
        impactOccurred: jest.fn(),
        notificationOccurred: jest.fn(),
        selectionChanged: jest.fn(),
      },
      
      onEvent: jest.fn(),
      offEvent: jest.fn(),
      sendData: jest.fn(),
    },
  },
  writable: true,
})

// Mock IntersectionObserver
global.IntersectionObserver = class IntersectionObserver {
  constructor() {}
  disconnect() {}
  observe() {}
  unobserve() {}
}

// Mock ResizeObserver
global.ResizeObserver = class ResizeObserver {
  constructor() {}
  disconnect() {}
  observe() {}
  unobserve() {}
}

// Mock matchMedia
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: jest.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: jest.fn(), // deprecated
    removeListener: jest.fn(), // deprecated
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
    dispatchEvent: jest.fn(),
  })),
})

// Mock localStorage
const localStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
}
global.localStorage = localStorageMock

// Mock sessionStorage
const sessionStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
}
global.sessionStorage = sessionStorageMock

// Mock fetch
global.fetch = jest.fn()

// Mock console methods to reduce noise in tests
global.console = {
  ...console,
  log: jest.fn(),
  debug: jest.fn(),
  info: jest.fn(),
  warn: jest.fn(),
  error: jest.fn(),
}
