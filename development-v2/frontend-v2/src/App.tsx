import React, { useEffect, useState } from 'react'
import { Routes, Route, Navigate } from 'react-router-dom'
import { useQuery } from '@tanstack/react-query'

// Layout components
import DesktopLayout from '@/components/layout/DesktopLayout'
import MobileLayout from '@/components/layout/MobileLayout'
import TelegramWebAppLayout from '@/components/layout/TelegramWebAppLayout'

// Pages
import LoginPage from '@/pages/LoginPage'
import DashboardPage from '@/pages/DashboardPage'
import AnalysisPage from '@/pages/AnalysisPage'
import DesktopAnalysisPage from '@/pages/DesktopAnalysisPage'
import PlansPage from '@/pages/PlansPage'
import AdminPage from '@/pages/AdminPage'

// Hooks and utilities
import { useAuthStore } from '@/stores/authStore'
import { useDeviceDetection } from '@/hooks/useDeviceDetection'
import { useTelegramWebApp } from '@/hooks/useTelegramWebApp'
import { cn } from '@/lib/utils'

// Types
import type { User } from '@/types/auth'

function App() {
  const { isDesktop, isMobile, isTablet } = useDeviceDetection()
  const { isWebApp, webApp, user: tgUser } = useTelegramWebApp()
  const { user, token, isAuthenticated, initializeAuth, setUser } = useAuthStore()
  const [isInitialized, setIsInitialized] = useState(false)

  // Initialize authentication on app start
  useEffect(() => {
    const init = async () => {
      try {
        await initializeAuth()
        
        // If we're in Telegram WebApp, try to authenticate with Telegram data
        if (isWebApp && tgUser && !isAuthenticated) {
          // TODO: Implement Telegram WebApp authentication
          console.log('Telegram WebApp user:', tgUser)
        }
      } catch (error) {
        console.error('Failed to initialize auth:', error)
      } finally {
        setIsInitialized(true)
      }
    }

    init()
  }, [initializeAuth, isWebApp, tgUser, isAuthenticated])

  // Query to fetch user profile if authenticated
  const { data: userProfile } = useQuery({
    queryKey: ['user', 'profile'],
    queryFn: async (): Promise<User> => {
      const apiBaseUrl = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8001'
      const response = await fetch(`${apiBaseUrl}/api/v2/auth/profile`, {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      })

      if (!response.ok) {
        throw new Error('Failed to fetch user profile')
      }

      return response.json()
    },
    enabled: !!token && isAuthenticated,
    staleTime: 1000 * 60 * 5, // 5 minutes
  })

  // Update user in store when profile is fetched
  useEffect(() => {
    if (userProfile) {
      setUser(userProfile)
    }
  }, [userProfile, setUser])

  // Show loading state while initializing
  if (!isInitialized) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-background">
        <div className="flex flex-col items-center gap-4">
          <div className="loading-spinner" />
          <p className="text-sm text-muted-foreground">
            Инициализация ChartGenius v2...
          </p>
        </div>
      </div>
    )
  }

  // Determine layout based on device and context
  const getLayout = () => {
    if (isWebApp) {
      return TelegramWebAppLayout
    }
    
    if (isDesktop) {
      return DesktopLayout
    }
    
    return MobileLayout
  }

  const Layout = getLayout()

  return (
    <div className={cn(
      "min-h-screen bg-background text-foreground",
      isWebApp && "twa-viewport",
      isDesktop && "desktop-layout",
      isMobile && "mobile-layout"
    )}>
      <Layout>
        <Routes>
          {/* Root route - redirect based on context */}
          <Route
            path="/"
            element={
              isAuthenticated ? (
                isWebApp ? (
                  <Navigate to="/dashboard" replace />
                ) : isDesktop ? (
                  <Navigate to="/desktop" replace />
                ) : (
                  <Navigate to="/dashboard" replace />
                )
              ) : (
                <Navigate to="/login" replace />
              )
            }
          />

          {/* Authentication routes */}
          <Route
            path="/login"
            element={
              isAuthenticated ? (
                <Navigate to="/dashboard" replace />
              ) : (
                <LoginPage />
              )
            }
          />

          {/* Protected routes */}
          <Route
            path="/dashboard"
            element={
              isAuthenticated ? (
                <DashboardPage />
              ) : (
                <Navigate to="/login" replace />
              )
            }
          />

          <Route
            path="/analysis"
            element={
              isAuthenticated ? (
                <AnalysisPage />
              ) : (
                <Navigate to="/login" replace />
              )
            }
          />

          <Route
            path="/plans"
            element={<PlansPage />}
          />

          {/* Desktop-specific route */}
          <Route
            path="/desktop"
            element={<DesktopAnalysisPage />}
          />

          {/* Admin routes */}
          <Route
            path="/admin/*"
            element={
              isAuthenticated && user?.role === 'admin' ? (
                <AdminPage />
              ) : (
                <Navigate to="/dashboard" replace />
              )
            }
          />

          {/* Catch all route */}
          <Route
            path="*"
            element={<Navigate to="/" replace />}
          />
        </Routes>
      </Layout>
    </div>
  )
}

export default App
