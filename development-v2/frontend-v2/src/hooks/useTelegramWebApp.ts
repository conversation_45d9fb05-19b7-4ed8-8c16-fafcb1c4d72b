import { useState, useEffect, useCallback } from 'react'
import type { TelegramWebAppUser, TelegramWebAppInitData } from '@/types/auth'

// Extend Window interface for Telegram WebApp
declare global {
  interface Window {
    Telegram?: {
      WebApp: {
        initData: string
        initDataUnsafe: TelegramWebAppInitData
        version: string
        platform: string
        colorScheme: 'light' | 'dark'
        themeParams: Record<string, string>
        isExpanded: boolean
        viewportHeight: number
        viewportStableHeight: number
        headerColor: string
        backgroundColor: string
        isClosingConfirmationEnabled: boolean
        
        // Methods
        ready(): void
        expand(): void
        close(): void
        enableClosingConfirmation(): void
        disableClosingConfirmation(): void
        showPopup(params: {
          title?: string
          message: string
          buttons?: Array<{
            id?: string
            type?: 'default' | 'ok' | 'close' | 'cancel' | 'destructive'
            text: string
          }>
        }, callback?: (buttonId: string) => void): void
        showAlert(message: string, callback?: () => void): void
        showConfirm(message: string, callback?: (confirmed: boolean) => void): void
        showScanQrPopup(params: {
          text?: string
        }, callback?: (text: string) => boolean): void
        closeScanQrPopup(): void
        readTextFromClipboard(callback?: (text: string) => void): void
        requestWriteAccess(callback?: (granted: boolean) => void): void
        requestContact(callback?: (granted: boolean) => void): void
        
        // Main Button
        MainButton: {
          text: string
          color: string
          textColor: string
          isVisible: boolean
          isActive: boolean
          isProgressVisible: boolean
          setText(text: string): void
          onClick(callback: () => void): void
          offClick(callback: () => void): void
          show(): void
          hide(): void
          enable(): void
          disable(): void
          showProgress(leaveActive?: boolean): void
          hideProgress(): void
          setParams(params: {
            text?: string
            color?: string
            text_color?: string
            is_active?: boolean
            is_visible?: boolean
          }): void
        }
        
        // Back Button
        BackButton: {
          isVisible: boolean
          onClick(callback: () => void): void
          offClick(callback: () => void): void
          show(): void
          hide(): void
        }
        
        // Haptic Feedback
        HapticFeedback: {
          impactOccurred(style: 'light' | 'medium' | 'heavy' | 'rigid' | 'soft'): void
          notificationOccurred(type: 'error' | 'success' | 'warning'): void
          selectionChanged(): void
        }
        
        // Events
        onEvent(eventType: string, callback: Function): void
        offEvent(eventType: string, callback: Function): void
        sendData(data: string): void
      }
    }
  }
}

interface TelegramWebAppHook {
  isWebApp: boolean
  webApp: Window['Telegram']['WebApp'] | null
  user: TelegramWebAppUser | null
  initData: string
  initDataUnsafe: TelegramWebAppInitData | null
  isReady: boolean
  colorScheme: 'light' | 'dark'
  themeParams: Record<string, string>
  
  // Methods
  showMainButton: (text: string, onClick: () => void) => void
  hideMainButton: () => void
  showBackButton: (onClick: () => void) => void
  hideBackButton: () => void
  showAlert: (message: string) => Promise<void>
  showConfirm: (message: string) => Promise<boolean>
  hapticFeedback: (type: 'impact' | 'notification' | 'selection', style?: string) => void
  close: () => void
  expand: () => void
}

export function useTelegramWebApp(): TelegramWebAppHook {
  const [isReady, setIsReady] = useState(false)
  const [webApp, setWebApp] = useState<Window['Telegram']['WebApp'] | null>(null)
  const [user, setUser] = useState<TelegramWebAppUser | null>(null)
  const [initData, setInitData] = useState('')
  const [initDataUnsafe, setInitDataUnsafe] = useState<TelegramWebAppInitData | null>(null)
  const [colorScheme, setColorScheme] = useState<'light' | 'dark'>('dark')
  const [themeParams, setThemeParams] = useState<Record<string, string>>({})

  const isWebApp = typeof window !== 'undefined' && !!window.Telegram?.WebApp

  useEffect(() => {
    if (!isWebApp) {
      setIsReady(true)
      return
    }

    const tg = window.Telegram!.WebApp

    // Initialize WebApp
    tg.ready()
    tg.expand()

    // Set state
    setWebApp(tg)
    setInitData(tg.initData)
    setInitDataUnsafe(tg.initDataUnsafe)
    setUser(tg.initDataUnsafe.user || null)
    setColorScheme(tg.colorScheme)
    setThemeParams(tg.themeParams)
    setIsReady(true)

    // Apply theme to document
    if (tg.colorScheme === 'dark') {
      document.documentElement.classList.add('dark')
    } else {
      document.documentElement.classList.remove('dark')
    }

    // Listen for theme changes
    const handleThemeChanged = () => {
      setColorScheme(tg.colorScheme)
      setThemeParams(tg.themeParams)
      
      if (tg.colorScheme === 'dark') {
        document.documentElement.classList.add('dark')
      } else {
        document.documentElement.classList.remove('dark')
      }
    }

    tg.onEvent('themeChanged', handleThemeChanged)

    // Cleanup
    return () => {
      tg.offEvent('themeChanged', handleThemeChanged)
    }
  }, [isWebApp])

  const showMainButton = useCallback((text: string, onClick: () => void) => {
    if (!webApp) return

    webApp.MainButton.setText(text)
    webApp.MainButton.onClick(onClick)
    webApp.MainButton.show()
  }, [webApp])

  const hideMainButton = useCallback(() => {
    if (!webApp) return

    webApp.MainButton.hide()
  }, [webApp])

  const showBackButton = useCallback((onClick: () => void) => {
    if (!webApp) return

    webApp.BackButton.onClick(onClick)
    webApp.BackButton.show()
  }, [webApp])

  const hideBackButton = useCallback(() => {
    if (!webApp) return

    webApp.BackButton.hide()
  }, [webApp])

  const showAlert = useCallback((message: string): Promise<void> => {
    return new Promise((resolve) => {
      if (!webApp) {
        alert(message)
        resolve()
        return
      }

      webApp.showAlert(message, () => resolve())
    })
  }, [webApp])

  const showConfirm = useCallback((message: string): Promise<boolean> => {
    return new Promise((resolve) => {
      if (!webApp) {
        resolve(confirm(message))
        return
      }

      webApp.showConfirm(message, (confirmed) => resolve(confirmed))
    })
  }, [webApp])

  const hapticFeedback = useCallback((
    type: 'impact' | 'notification' | 'selection',
    style?: string
  ) => {
    if (!webApp?.HapticFeedback) return

    switch (type) {
      case 'impact':
        webApp.HapticFeedback.impactOccurred(
          (style as 'light' | 'medium' | 'heavy' | 'rigid' | 'soft') || 'medium'
        )
        break
      case 'notification':
        webApp.HapticFeedback.notificationOccurred(
          (style as 'error' | 'success' | 'warning') || 'success'
        )
        break
      case 'selection':
        webApp.HapticFeedback.selectionChanged()
        break
    }
  }, [webApp])

  const close = useCallback(() => {
    if (!webApp) return
    webApp.close()
  }, [webApp])

  const expand = useCallback(() => {
    if (!webApp) return
    webApp.expand()
  }, [webApp])

  return {
    isWebApp,
    webApp,
    user,
    initData,
    initDataUnsafe,
    isReady,
    colorScheme,
    themeParams,
    showMainButton,
    hideMainButton,
    showBackButton,
    hideBackButton,
    showAlert,
    showConfirm,
    hapticFeedback,
    close,
    expand,
  }
}

// Helper hook for Telegram WebApp authentication
export function useTelegramAuth() {
  const { isWebApp, initData, user } = useTelegramWebApp()
  
  const getTelegramAuthData = useCallback(() => {
    if (!isWebApp || !initData) {
      return null
    }

    return {
      init_data: initData,
      telegram_id: user?.id?.toString(),
      username: user?.username,
      first_name: user?.first_name,
      last_name: user?.last_name,
      language_code: user?.language_code,
    }
  }, [isWebApp, initData, user])

  return {
    isWebApp,
    user,
    getTelegramAuthData,
    canAuthenticate: isWebApp && !!initData && !!user,
  }
}
