import { useState, useEffect } from 'react'

interface DeviceInfo {
  isDesktop: boolean
  isMobile: boolean
  isTablet: boolean
  screenWidth: number
  screenHeight: number
  userAgent: string
  isTouchDevice: boolean
}

export function useDeviceDetection(): DeviceInfo {
  const [deviceInfo, setDeviceInfo] = useState<DeviceInfo>(() => {
    // Initial values (will be updated on client)
    if (typeof window === 'undefined') {
      return {
        isDesktop: true,
        isMobile: false,
        isTablet: false,
        screenWidth: 1920,
        screenHeight: 1080,
        userAgent: '',
        isTouchDevice: false,
      }
    }

    const width = window.innerWidth
    const userAgent = navigator.userAgent
    const isTouchDevice = 'ontouchstart' in window || navigator.maxTouchPoints > 0

    return {
      isDesktop: width >= 1280,
      isMobile: width <= 768,
      isTablet: width > 768 && width < 1280,
      screenWidth: width,
      screenHeight: window.innerHeight,
      userAgent,
      isTouchDevice,
    }
  })

  useEffect(() => {
    const updateDeviceInfo = () => {
      const width = window.innerWidth
      const height = window.innerHeight
      const userAgent = navigator.userAgent
      const isTouchDevice = 'ontouchstart' in window || navigator.maxTouchPoints > 0

      setDeviceInfo({
        isDesktop: width >= 1280,
        isMobile: width <= 768,
        isTablet: width > 768 && width < 1280,
        screenWidth: width,
        screenHeight: height,
        userAgent,
        isTouchDevice,
      })
    }

    // Update on resize
    window.addEventListener('resize', updateDeviceInfo)
    
    // Update on orientation change (mobile devices)
    window.addEventListener('orientationchange', () => {
      // Delay to ensure dimensions are updated
      setTimeout(updateDeviceInfo, 100)
    })

    // Initial update
    updateDeviceInfo()

    return () => {
      window.removeEventListener('resize', updateDeviceInfo)
      window.removeEventListener('orientationchange', updateDeviceInfo)
    }
  }, [])

  return deviceInfo
}

// Additional device detection utilities
export function isMobileDevice(): boolean {
  if (typeof window === 'undefined') return false
  
  const userAgent = navigator.userAgent.toLowerCase()
  const mobileKeywords = [
    'android', 'webos', 'iphone', 'ipad', 'ipod', 
    'blackberry', 'windows phone', 'mobile'
  ]
  
  return mobileKeywords.some(keyword => userAgent.includes(keyword))
}

export function isIOS(): boolean {
  if (typeof window === 'undefined') return false
  
  return /iPad|iPhone|iPod/.test(navigator.userAgent)
}

export function isAndroid(): boolean {
  if (typeof window === 'undefined') return false
  
  return /Android/.test(navigator.userAgent)
}

export function isSafari(): boolean {
  if (typeof window === 'undefined') return false
  
  const userAgent = navigator.userAgent
  return /Safari/.test(userAgent) && !/Chrome/.test(userAgent)
}

export function isChrome(): boolean {
  if (typeof window === 'undefined') return false
  
  return /Chrome/.test(navigator.userAgent)
}

export function isFirefox(): boolean {
  if (typeof window === 'undefined') return false
  
  return /Firefox/.test(navigator.userAgent)
}

// Hook for responsive breakpoints
export function useBreakpoint() {
  const { screenWidth } = useDeviceDetection()
  
  return {
    isSm: screenWidth >= 640,
    isMd: screenWidth >= 768,
    isLg: screenWidth >= 1024,
    isXl: screenWidth >= 1280,
    is2Xl: screenWidth >= 1536,
    
    // Custom breakpoints for ChartGenius
    isDesktop: screenWidth >= 1280,
    isTablet: screenWidth >= 768 && screenWidth < 1280,
    isMobile: screenWidth < 768,
  }
}

// Hook for detecting network connection
export function useNetworkStatus() {
  const [isOnline, setIsOnline] = useState(
    typeof navigator !== 'undefined' ? navigator.onLine : true
  )
  const [connectionType, setConnectionType] = useState<string>('unknown')

  useEffect(() => {
    const updateOnlineStatus = () => {
      setIsOnline(navigator.onLine)
    }

    const updateConnectionType = () => {
      // @ts-ignore - navigator.connection is experimental
      const connection = navigator.connection || navigator.mozConnection || navigator.webkitConnection
      if (connection) {
        setConnectionType(connection.effectiveType || 'unknown')
      }
    }

    window.addEventListener('online', updateOnlineStatus)
    window.addEventListener('offline', updateOnlineStatus)
    
    // @ts-ignore
    if (navigator.connection) {
      // @ts-ignore
      navigator.connection.addEventListener('change', updateConnectionType)
      updateConnectionType()
    }

    return () => {
      window.removeEventListener('online', updateOnlineStatus)
      window.removeEventListener('offline', updateOnlineStatus)
      
      // @ts-ignore
      if (navigator.connection) {
        // @ts-ignore
        navigator.connection.removeEventListener('change', updateConnectionType)
      }
    }
  }, [])

  return {
    isOnline,
    connectionType,
    isSlowConnection: connectionType === 'slow-2g' || connectionType === '2g',
    isFastConnection: connectionType === '4g' || connectionType === '5g',
  }
}

// Hook for detecting if user prefers reduced motion
export function usePrefersReducedMotion() {
  const [prefersReducedMotion, setPrefersReducedMotion] = useState(false)

  useEffect(() => {
    const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)')
    setPrefersReducedMotion(mediaQuery.matches)

    const handleChange = (event: MediaQueryListEvent) => {
      setPrefersReducedMotion(event.matches)
    }

    mediaQuery.addEventListener('change', handleChange)
    return () => mediaQuery.removeEventListener('change', handleChange)
  }, [])

  return prefersReducedMotion
}
