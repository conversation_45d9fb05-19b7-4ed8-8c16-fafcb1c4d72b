// Authentication types based on ТЗ R6/R7 requirements

export interface User {
  id: string
  telegram_id: string
  email?: string
  role: UserRole
  tier: UserTier
  exp_ts?: number
  created_at: string
  updated_at: string
  
  // Profile information
  first_name?: string
  last_name?: string
  username?: string
  language_code?: string
  
  // Subscription information
  subscription?: Subscription
  
  // Settings
  settings?: UserSettings
}

export type UserRole = 'user' | 'premium' | 'vip' | 'admin'

export type UserTier = 'free' | 'premium' | 'vip'

export interface Subscription {
  id: string
  user_id: string
  tier: UserTier
  price: number
  exp_ts: number
  source: 'stripe' | 'telegram'
  status: 'active' | 'expired' | 'cancelled'
  created_at: string
  updated_at: string
}

export interface UserSettings {
  language: 'ru' | 'en'
  timezone: string
  notifications: {
    signals: boolean
    news: boolean
    system: boolean
  }
  trading: {
    default_symbol: string
    default_timeframe: string
    risk_level: 'low' | 'medium' | 'high'
  }
  ui: {
    theme: 'light' | 'dark' | 'auto'
    compact_mode: boolean
    show_tooltips: boolean
  }
}

export interface AuthState {
  user: User | null
  token: string | null
  isAuthenticated: boolean
  isLoading: boolean
  error: string | null
}

export interface LoginRequest {
  telegram_id?: string
  init_data?: string // Telegram WebApp init data
  username?: string
  email?: string
}

export interface LoginResponse {
  user: User
  token: string
  expires_in: number
}

export interface RefreshTokenRequest {
  refresh_token: string
}

export interface RefreshTokenResponse {
  token: string
  expires_in: number
}

// Telegram WebApp types
export interface TelegramWebAppUser {
  id: number
  first_name: string
  last_name?: string
  username?: string
  language_code?: string
  is_premium?: boolean
  photo_url?: string
}

export interface TelegramWebAppInitData {
  user?: TelegramWebAppUser
  chat_instance?: string
  chat_type?: string
  auth_date: number
  hash: string
}

// JWT Token payload
export interface JWTPayload {
  telegram_id: string
  role: UserRole
  tier: UserTier
  exp: number
  iat: number
}

// Permission types
export type Permission = 
  | 'analysis:read'
  | 'analysis:create'
  | 'signals:read'
  | 'watchlist:read'
  | 'watchlist:write'
  | 'admin:users'
  | 'admin:prompts'
  | 'admin:llm'
  | 'admin:broadcast'
  | 'moderator:ban'
  | 'moderator:review'

export const ROLE_PERMISSIONS: Record<UserRole, Permission[]> = {
  user: [
    'analysis:read',
  ],
  premium: [
    'analysis:read',
    'analysis:create',
    'signals:read',
    'watchlist:read',
    'watchlist:write',
  ],
  vip: [
    'analysis:read',
    'analysis:create',
    'signals:read',
    'watchlist:read',
    'watchlist:write',
  ],
  admin: [
    'analysis:read',
    'analysis:create',
    'signals:read',
    'watchlist:read',
    'watchlist:write',
    'admin:users',
    'admin:prompts',
    'admin:llm',
    'admin:broadcast',
    'moderator:ban',
    'moderator:review',
  ],
}

// Helper functions
export const hasPermission = (user: User | null, permission: Permission): boolean => {
  if (!user) return false
  return ROLE_PERMISSIONS[user.role]?.includes(permission) ?? false
}

export const isSubscriptionActive = (user: User | null): boolean => {
  if (!user?.subscription) return false
  
  const now = Date.now() / 1000
  return user.subscription.status === 'active' && 
         (user.subscription.exp_ts > now || user.subscription.exp_ts === 0)
}

export const getSubscriptionDaysLeft = (user: User | null): number => {
  if (!user?.subscription || !isSubscriptionActive(user)) return 0
  
  if (user.subscription.exp_ts === 0) return Infinity // Lifetime subscription
  
  const now = Date.now() / 1000
  const daysLeft = Math.ceil((user.subscription.exp_ts - now) / (24 * 60 * 60))
  return Math.max(0, daysLeft)
}
