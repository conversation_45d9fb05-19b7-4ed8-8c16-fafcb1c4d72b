<!doctype html>
<html lang="ru" class="dark">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>ChartGenius v2 - AI Crypto Analysis</title>
    
    <!-- Meta tags for SEO -->
    <meta name="description" content="ChartGenius v2 - Advanced AI-powered cryptocurrency analysis platform with real-time charts and trading signals" />
    <meta name="keywords" content="crypto, trading, analysis, AI, charts, signals, bitcoin, ethereum" />
    <meta name="author" content="ChartGenius Team" />
    
    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://chartgenius.dev/" />
    <meta property="og:title" content="ChartGenius v2 - AI Crypto Analysis" />
    <meta property="og:description" content="Advanced AI-powered cryptocurrency analysis platform" />
    <meta property="og:image" content="/og-image.png" />
    
    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image" />
    <meta property="twitter:url" content="https://chartgenius.dev/" />
    <meta property="twitter:title" content="ChartGenius v2 - AI Crypto Analysis" />
    <meta property="twitter:description" content="Advanced AI-powered cryptocurrency analysis platform" />
    <meta property="twitter:image" content="/twitter-image.png" />
    
    <!-- Telegram WebApp -->
    <script src="https://telegram.org/js/telegram-web-app.js"></script>
    
    <!-- Preload critical fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Critical CSS for above-the-fold content -->
    <style>
      /* Critical CSS to prevent FOUC */
      body {
        margin: 0;
        font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
        background-color: #0d1117;
        color: #ffffff;
        line-height: 1.6;
      }
      
      #root {
        min-height: 100vh;
        display: flex;
        flex-direction: column;
      }
      
      /* Loading spinner */
      .loading-spinner {
        display: inline-block;
        width: 40px;
        height: 40px;
        border: 3px solid #374151;
        border-radius: 50%;
        border-top-color: #34d399;
        animation: spin 1s ease-in-out infinite;
      }
      
      @keyframes spin {
        to { transform: rotate(360deg); }
      }
      
      /* Hide scrollbar but keep functionality */
      ::-webkit-scrollbar {
        width: 6px;
      }
      
      ::-webkit-scrollbar-track {
        background: #1f2937;
      }
      
      ::-webkit-scrollbar-thumb {
        background: #374151;
        border-radius: 3px;
      }
      
      ::-webkit-scrollbar-thumb:hover {
        background: #4b5563;
      }
    </style>
  </head>
  <body>
    <div id="root">
      <!-- Fallback loading state -->
      <div style="display: flex; justify-content: center; align-items: center; height: 100vh; flex-direction: column; gap: 16px;">
        <div class="loading-spinner"></div>
        <p style="color: #9ca3af; font-size: 14px;">Загрузка ChartGenius v2...</p>
      </div>
    </div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
