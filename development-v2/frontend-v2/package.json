{"name": "<PERSON><PERSON><PERSON>-frontend-v2", "version": "2.0.0-dev", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "test:e2e": "playwright test", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext ts,tsx --fix", "type-check": "tsc --noEmit"}, "dependencies": {"react": "^19.0.0", "react-dom": "^19.0.0", "react-router-dom": "^6.26.0", "@tanstack/react-query": "^5.51.0", "@tanstack/react-query-devtools": "^5.51.0", "zod": "^3.23.8", "zustand": "^4.5.4", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-dialog": "^1.1.1", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-toast": "^1.2.1", "@radix-ui/react-tooltip": "^1.1.2", "@radix-ui/react-accordion": "^1.2.0", "@radix-ui/react-tabs": "^1.1.0", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "tailwind-merge": "^2.4.0", "lucide-react": "^0.408.0", "lightweight-charts": "^5.0.7", "recharts": "^2.12.7", "date-fns": "^3.6.0", "react-hook-form": "^7.52.1", "@hookform/resolvers": "^3.9.0", "stripe": "^16.5.0", "@stripe/stripe-js": "^4.1.0", "@stripe/react-stripe-js": "^2.7.3"}, "devDependencies": {"@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@typescript-eslint/eslint-plugin": "^7.15.0", "@typescript-eslint/parser": "^7.15.0", "@vitejs/plugin-react": "^4.3.1", "eslint": "^8.57.0", "eslint-plugin-react-hooks": "^4.6.2", "eslint-plugin-react-refresh": "^0.4.7", "typescript": "^5.2.2", "vite": "^5.3.4", "vitest": "^2.0.3", "@vitest/ui": "^2.0.3", "jsdom": "^24.1.1", "@testing-library/react": "^16.0.0", "@testing-library/jest-dom": "^6.4.6", "@testing-library/user-event": "^14.5.2", "playwright": "^1.45.1", "@playwright/test": "^1.45.1", "tailwindcss": "^4.0.0-alpha.19", "@tailwindcss/typography": "^0.5.13", "@tailwindcss/forms": "^0.5.7", "@tailwindcss/aspect-ratio": "^0.4.2", "autoprefixer": "^10.4.19", "postcss": "^8.4.39"}, "engines": {"node": ">=20.0.0", "npm": ">=10.0.0"}}