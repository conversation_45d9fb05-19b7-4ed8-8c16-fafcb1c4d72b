# ChartGenius Frontend v2

Современный фронтенд для платформы анализа криптовалют на базе React 19 и Tailwind CSS v4.

## 🚀 Технологический стек

- **React 19** - с поддержкой React Server Components
- **TypeScript** - строгая типизация
- **Tailwind CSS v4** - современная стилизация
- **Vite** - быстрая сборка и разработка
- **TanStack Query v5** - управление состоянием сервера
- **Zustand** - управление клиентским состоянием
- **React Router v6** - маршрутизация
- **shadcn/ui** - компоненты UI
- **Vitest** - тестирование
- **Playwright** - E2E тестирование

## 📱 Поддерживаемые платформы

- **Desktop** (≥1280px) - полнофункциональный интерфейс
- **Mobile** (≤768px) - адаптивный интерфейс
- **Telegram Mini App** - интеграция с Telegram WebApp

## 🛠️ Разработка

### Установка зависимостей
```bash
npm install
```

### Запуск dev сервера
```bash
npm run dev
```

Приложение будет доступно по адресу: http://localhost:3001

### Сборка для production
```bash
npm run build
```

### Предварительный просмотр production сборки
```bash
npm run preview
```

## 🧪 Тестирование

### Unit тесты
```bash
npm run test
```

### Unit тесты с UI
```bash
npm run test:ui
```

### E2E тесты
```bash
npm run test:e2e
```

## 📁 Структура проекта

```
src/
├── components/          # Переиспользуемые компоненты
│   ├── layout/         # Компоненты макета
│   └── ui/             # Базовые UI компоненты
├── hooks/              # Пользовательские хуки
├── lib/                # Утилиты и конфигурация
├── pages/              # Страницы приложения
├── stores/             # Zustand stores
├── types/              # TypeScript типы
├── utils/              # Вспомогательные функции
└── test/               # Настройки тестирования
```

## 🎨 Дизайн система

Проект использует дизайн-токены из `tokens.chartgenius.json`:

- **Цвета**: primary, secondary, neutral, danger
- **Радиусы**: sm (4px), md (6px), lg (8px)
- **Отступы**: xs (4px), sm (8px), md (16px), lg (24px)
- **Шрифты**: Inter (основной), Menlo (моноширинный)

## 📱 Responsive дизайн

### Breakpoints
- `mobile`: ≤768px
- `tablet`: 769px - 1279px
- `desktop`: ≥1280px

### Layout стратегия
- **Mobile-first** подход
- **Desktop-enhanced** для экранов ≥1280px
- **Telegram WebApp** оптимизация

## 🔧 Конфигурация

### Environment переменные

Создайте `.env.local` файл:

```env
VITE_API_BASE_URL=http://localhost:8001/api/v2
VITE_TELEGRAM_BOT_USERNAME=Chart_Genius_bot
VITE_STRIPE_PUBLISHABLE_KEY=pk_test_...
```

### Tailwind CSS

Конфигурация в `tailwind.config.ts` включает:
- Кастомные цвета для криптотрейдинга
- Анимации и переходы
- Responsive утилиты
- Dark mode поддержка

## 🔌 API интеграция

### Базовая конфигурация
```typescript
const API_BASE_URL = '/api/v2'
```

### Аутентификация
- JWT токены с RS256
- Автоматическое обновление токенов
- Telegram WebApp аутентификация

### Запросы
- TanStack Query для кэширования
- Автоматический retry при ошибках
- Optimistic updates

## 📲 Telegram WebApp

### Инициализация
```typescript
import { useTelegramWebApp } from '@/hooks/useTelegramWebApp'

const { isWebApp, webApp, user } = useTelegramWebApp()
```

### Возможности
- Haptic feedback
- Main/Back кнопки
- Theme integration
- Native alerts/confirms

## 🎯 Производительность

### Оптимизации
- Code splitting по маршрутам
- Lazy loading компонентов
- Image optimization
- Bundle analysis

### Метрики
- Lighthouse Score > 90
- LCP < 2s
- FID < 100ms
- CLS < 0.1

## 🔒 Безопасность

- XSS защита
- CSRF токены
- Content Security Policy
- Secure headers

## 🚀 Деплой

### Vercel (рекомендуется)
```bash
npm run build
vercel --prod
```

### Docker
```bash
docker build -t chartgenius-frontend-v2 .
docker run -p 3001:3001 chartgenius-frontend-v2
```

## 📚 Документация

- [React 19 Docs](https://react.dev/)
- [Tailwind CSS v4](https://tailwindcss.com/)
- [TanStack Query](https://tanstack.com/query/)
- [Telegram WebApp API](https://core.telegram.org/bots/webapps)

## 🤝 Вклад в разработку

1. Создайте feature branch
2. Внесите изменения
3. Добавьте тесты
4. Создайте Pull Request

## 📄 Лицензия

© 2025 ChartGenius. Все права защищены.
