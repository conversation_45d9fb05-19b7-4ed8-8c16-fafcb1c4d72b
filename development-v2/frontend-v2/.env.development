# Development environment variables for ChartGenius v2
VITE_APP_VERSION=2.0.0-dev
VITE_APP_NAME=ChartGenius
VITE_APP_DESCRIPTION=AI-powered cryptocurrency analysis platform

# API Configuration
VITE_API_BASE_URL=http://localhost:8001/api/v2
VITE_WS_BASE_URL=ws://localhost:8001/ws

# Telegram Configuration
VITE_TELEGRAM_BOT_USERNAME=Chart_Genius_bot
VITE_TELEGRAM_BOT_URL=https://t.me/Chart_Genius_bot

# Feature Flags
VITE_ENABLE_TELEGRAM_WEBAPP=true
VITE_ENABLE_DESKTOP_MODE=true
VITE_ENABLE_DEMO_MODE=true
VITE_ENABLE_DEV_TOOLS=true

# Development Settings
VITE_LOG_LEVEL=debug
VITE_ENABLE_MOCK_DATA=true
VITE_ENABLE_ERROR_BOUNDARY=true

# Stripe Configuration (Development)
VITE_STRIPE_PUBLISHABLE_KEY=pk_test_...

# Analytics (Development)
VITE_ENABLE_ANALYTICS=false
VITE_ANALYTICS_ID=

# Build Configuration
VITE_BUILD_TIME=
VITE_GIT_COMMIT=
VITE_GIT_BRANCH=development-v2
