# ChartGenius v2 Development Environment

**Версия:** v2.0.0-dev  
**Статус:** 🚧 В разработке  
**Основано на:** ТЗ R-7 (ChartGenius_TZ/ChartGenius_TZ_R7.md)  
**Дата создания:** 01 июля 2025  

---

## 🎯 Цели релиза v2

1. **Сохранить функционал** Chart Genius v1.0.51
2. **Современный UI** - Desktop ≥1280px + Telegram Mini App
3. **Монетизация** - Stripe Checkout + Telegram Payments
4. **Gemini CLI** - альтернативная LLM с переключателем
5. **Oracle Cloud** - развертывание в Always-Free tier

---

## 🏗️ Архитектура v2

### **Технологический стек (обновленный 2025):**
- **Frontend**: React 19 + RSC, Tailwind CSS v4.0, shadcn/ui, TanStack Query v5
- **Backend**: FastAPI 0.115.14, Python 3.12, Pydantic v2, SQLModel
- **Bot**: aiogram 3.20.0 (миграция с python-telegram-bot)
- **Database**: Oracle Autonomous JSON DB (миграция с Firestore)
- **Infrastructure**: Oracle Cloud Always-Free (4 OCPU Ampere A1 + 24GB RAM)

### **Сервисная архитектура:**
```
┌─────────── Browser ≥1280 ───────────┐
│ React 19 (RSC) + Stripe UI         │
└─────────────────────────────────────┘
          ▲  JWT RS256 / HTTPS 
┌─ Telegram Mini App (WebView) ───┐
│     aiogram 3.20.0 (webhook)    │
└─────────────────────────────────┘
          ▼
   ┌─ OCI LB (HTTPS) ─┐
┌──┴──────────┐ ┌────┴─────────┐
│ cg‑core     │ │ cg‑worker    │
│ FastAPI 0.115│ │ Gemini CLI   │
│ aiogram router│ │ TA‑workers   │
└──┬───────────┘ └──┬───────────┘
   │ Redis micro    │
   │ (Gemini limit) │
┌──▼──────────┐  ┌──▼──────────┐
│ Autonomous  │  │  Object     │
│  JSON DB    │  │  Storage    │
└─────────────┘  └─────────────┘
```

---

## 📁 Структура проекта v2

```
development-v2/
├── VERSION (v2.0.0-dev)
├── README-v2.md
├── frontend-v2/          # React 19 + Tailwind v4
├── backend-v2/           # FastAPI 0.115 + SQLModel
├── bot-v2/              # aiogram 3.20.0
├── terraform/           # Oracle Cloud Infrastructure
├── docs-v2/            # Документация v2
├── tests-v2/           # Тестирование
└── docker-compose-v2.yml
```

---

## 🚀 Быстрый старт

### **Разработка:**
```bash
cd development-v2/
./start-dev-v2.sh
```

### **Тестирование:**
```bash
cd development-v2/
./run-tests-v2.sh
```

---

## 📋 План разработки (Спринты S1-S8)

| Спринт | Цель | Критерий Done | Статус |
|--------|------|---------------|--------|
| S1 | React 19 каркас | Lighthouse > 90 | 🚧 В работе |
| S2 | Stripe Checkout | tier обновляется live | ⏳ Ожидание |
| S3 | Telegram WebApp / Payments | план покупается в TWA | ⏳ Ожидание |
| S4 | Bot v2 команды | `/signals` выдаёт PNG | ⏳ Ожидание |
| S5 | Admin UI | LLM switch работает | ⏳ Ожидание |
| S6 | Gemini CLI router | fallback OK | ⏳ Ожидание |
| S7 | Terraform stack | `terraform apply` ≤ 15 мин | ⏳ Ожидание |
| S8 | Нагруз‑тест | err < 0.1%, p95 < 300ms | ⏳ Ожидание |

---

## 🔗 Связанные документы

- **Основное ТЗ**: `../ChartGenius_TZ/ChartGenius_TZ_R6.md`
- **Дополнения**: `../ChartGenius_TZ/ChartGenius_TZ_R7.md`
- **API Спецификация**: `../ChartGenius_TZ/openapi.yaml`
- **Схема БД**: `../ChartGenius_TZ/er.ddl.sql`
- **Дизайн токены**: `../ChartGenius_TZ/tokens.chartgenius.json`

---

## ⚠️ Важные принципы

1. **Стабильность production** - никаких изменений в `../production/`
2. **Изолированная разработка** - полная независимость от v1
3. **Актуальные технологии** - версии библиотек 2025 года
4. **Поэтапная миграция** - следование спринтам ТЗ

---

**Статус:** 🚧 Активная разработка  
**Следующий этап:** S1 - React 19 каркас + Tailwind v4
