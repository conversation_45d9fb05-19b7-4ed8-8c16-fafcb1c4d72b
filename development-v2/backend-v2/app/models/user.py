"""
User model based on ChartGenius_TZ/er.ddl.sql schema
"""

from datetime import datetime
from enum import Enum
from typing import Optional

from sqlmodel import SQLModel, Field, Column, String, Integer, DateTime, JSON
from pydantic import validator


class UserRole(str, Enum):
    """User role enumeration"""
    USER = "user"
    PREMIUM = "premium"
    VIP = "vip"
    ADMIN = "admin"


class UserTier(str, Enum):
    """User tier enumeration"""
    FREE = "free"
    PREMIUM = "premium"
    VIP = "vip"


class UserSettings(SQLModel):
    """User settings model"""
    language: str = "ru"
    timezone: str = "Europe/Moscow"
    notifications: dict = {
        "signals": True,
        "news": True,
        "system": True,
    }
    trading: dict = {
        "default_symbol": "BTCUSDT",
        "default_timeframe": "4h",
        "risk_level": "medium",
    }
    ui: dict = {
        "theme": "dark",
        "compact_mode": False,
        "show_tooltips": True,
    }


class UserBase(SQLModel):
    """Base user model with common fields"""
    tg_id: str = Field(index=True, unique=True, description="Telegram ID")
    email: Optional[str] = Field(default=None, index=True)
    role: UserRole = Field(default=UserRole.USER)
    tier: UserTier = Field(default=UserTier.FREE)
    exp_ts: Optional[int] = Field(default=None, description="Subscription expiration timestamp")
    
    # Profile information
    first_name: Optional[str] = Field(default=None, max_length=100)
    last_name: Optional[str] = Field(default=None, max_length=100)
    username: Optional[str] = Field(default=None, max_length=100, index=True)
    language_code: Optional[str] = Field(default="ru", max_length=10)
    
    # Settings
    settings: Optional[UserSettings] = Field(default_factory=UserSettings)
    
    # Metadata
    is_active: bool = Field(default=True)
    is_banned: bool = Field(default=False)
    ban_reason: Optional[str] = Field(default=None)
    
    @validator("tg_id")
    def validate_telegram_id(cls, v):
        if not v or not v.isdigit():
            raise ValueError("Telegram ID must be a numeric string")
        return v
    
    @validator("email")
    def validate_email(cls, v):
        if v and "@" not in v:
            raise ValueError("Invalid email format")
        return v


class User(UserBase, table=True):
    """User database model"""
    __tablename__ = "users"
    
    id: Optional[int] = Field(default=None, primary_key=True)
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)
    last_login_at: Optional[datetime] = Field(default=None)
    
    # JSON fields for flexible data storage (Oracle AJD compatible)
    profile_data: Optional[dict] = Field(default=None, sa_column=Column(JSON))
    preferences: Optional[dict] = Field(default=None, sa_column=Column(JSON))
    
    # Statistics
    total_analyses: int = Field(default=0)
    total_signals_received: int = Field(default=0)
    
    def update_last_login(self):
        """Update last login timestamp"""
        self.last_login_at = datetime.utcnow()
        self.updated_at = datetime.utcnow()
    
    def is_subscription_active(self) -> bool:
        """Check if user has active subscription"""
        if not self.exp_ts:
            return self.tier != UserTier.FREE
        
        import time
        return self.exp_ts > int(time.time())
    
    def get_subscription_days_left(self) -> int:
        """Get days left in subscription"""
        if not self.exp_ts:
            return 0 if self.tier == UserTier.FREE else float('inf')
        
        import time
        days_left = (self.exp_ts - int(time.time())) / (24 * 60 * 60)
        return max(0, int(days_left))
    
    def has_permission(self, permission: str) -> bool:
        """Check if user has specific permission"""
        role_permissions = {
            UserRole.USER: [
                "analysis:read",
            ],
            UserRole.PREMIUM: [
                "analysis:read",
                "analysis:create",
                "signals:read",
                "watchlist:read",
                "watchlist:write",
            ],
            UserRole.VIP: [
                "analysis:read",
                "analysis:create",
                "signals:read",
                "watchlist:read",
                "watchlist:write",
                "signals:premium",
            ],
            UserRole.ADMIN: [
                "analysis:read",
                "analysis:create",
                "signals:read",
                "watchlist:read",
                "watchlist:write",
                "signals:premium",
                "admin:users",
                "admin:prompts",
                "admin:llm",
                "admin:broadcast",
                "moderator:ban",
                "moderator:review",
            ],
        }
        
        return permission in role_permissions.get(self.role, [])


class UserCreate(UserBase):
    """User creation model"""
    pass


class UserUpdate(SQLModel):
    """User update model"""
    email: Optional[str] = None
    role: Optional[UserRole] = None
    tier: Optional[UserTier] = None
    exp_ts: Optional[int] = None
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    username: Optional[str] = None
    language_code: Optional[str] = None
    settings: Optional[UserSettings] = None
    is_active: Optional[bool] = None
    is_banned: Optional[bool] = None
    ban_reason: Optional[str] = None


class UserResponse(UserBase):
    """User response model"""
    id: int
    created_at: datetime
    updated_at: datetime
    last_login_at: Optional[datetime]
    total_analyses: int
    total_signals_received: int
    
    # Computed fields
    subscription_active: bool
    subscription_days_left: int
    
    @classmethod
    def from_user(cls, user: User) -> "UserResponse":
        """Create response model from User instance"""
        return cls(
            id=user.id,
            tg_id=user.tg_id,
            email=user.email,
            role=user.role,
            tier=user.tier,
            exp_ts=user.exp_ts,
            first_name=user.first_name,
            last_name=user.last_name,
            username=user.username,
            language_code=user.language_code,
            settings=user.settings,
            is_active=user.is_active,
            is_banned=user.is_banned,
            ban_reason=user.ban_reason,
            created_at=user.created_at,
            updated_at=user.updated_at,
            last_login_at=user.last_login_at,
            total_analyses=user.total_analyses,
            total_signals_received=user.total_signals_received,
            subscription_active=user.is_subscription_active(),
            subscription_days_left=user.get_subscription_days_left(),
        )


class UserList(SQLModel):
    """User list response model"""
    users: list[UserResponse]
    total: int
    page: int
    size: int
    pages: int
