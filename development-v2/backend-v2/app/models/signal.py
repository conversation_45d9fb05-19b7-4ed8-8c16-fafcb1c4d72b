"""
Signal model based on ChartGenius_TZ/er.ddl.sql schema
"""

from datetime import datetime
from enum import Enum
from typing import Optional, List

from sqlmodel import SQLModel, Field, Column, JSON, Text
from pydantic import validator


class SignalType(str, Enum):
    """Signal type enumeration"""
    LONG = "long"
    SHORT = "short"
    HOLD = "hold"
    NEUTRAL = "neutral"


class SignalTimeframe(str, Enum):
    """Signal timeframe enumeration"""
    M1 = "1m"
    M5 = "5m"
    M15 = "15m"
    M30 = "30m"
    H1 = "1h"
    H4 = "4h"
    H12 = "12h"
    D1 = "1d"
    W1 = "1w"


class SignalStatus(str, Enum):
    """Signal status enumeration"""
    ACTIVE = "active"
    TRIGGERED = "triggered"
    EXPIRED = "expired"
    CANCELLED = "cancelled"


class SignalBase(SQLModel):
    """Base signal model"""
    symbol: str = Field(index=True, max_length=20, description="Trading symbol (e.g., BTCUSDT)")
    tf: SignalTimeframe = Field(description="Timeframe")
    text_md: str = Field(description="Signal analysis in Markdown format")
    ts: int = Field(description="Signal timestamp")
    
    # Signal details
    signal_type: Optional[SignalType] = Field(default=None)
    confidence: Optional[float] = Field(default=None, ge=0, le=100, description="Confidence percentage")
    
    # Price levels
    entry_price: Optional[float] = Field(default=None, gt=0)
    stop_loss: Optional[float] = Field(default=None, gt=0)
    take_profit: Optional[float] = Field(default=None, gt=0)
    
    # Risk management
    risk_level: Optional[str] = Field(default="medium", description="low, medium, high")
    position_size: Optional[float] = Field(default=None, ge=0, le=100, description="Position size percentage")
    
    @validator("symbol")
    def validate_symbol(cls, v):
        if not v or len(v) < 3:
            raise ValueError("Symbol must be at least 3 characters")
        return v.upper()
    
    @validator("ts")
    def validate_timestamp(cls, v):
        import time
        if v > int(time.time()) + 3600:  # Allow 1 hour in future
            raise ValueError("Timestamp cannot be too far in the future")
        return v


class Signal(SignalBase, table=True):
    """Signal database model"""
    __tablename__ = "signals"
    
    id: Optional[int] = Field(default=None, primary_key=True)
    status: SignalStatus = Field(default=SignalStatus.ACTIVE)
    
    # Metadata
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)
    expires_at: Optional[datetime] = Field(default=None)
    
    # Analysis metadata
    analysis_id: Optional[int] = Field(default=None, description="Related analysis ID")
    llm_provider: Optional[str] = Field(default=None, description="LLM provider used")
    llm_model: Optional[str] = Field(default=None, description="LLM model used")
    
    # Technical analysis data
    indicators: Optional[dict] = Field(default=None, sa_column=Column(JSON))
    market_data: Optional[dict] = Field(default=None, sa_column=Column(JSON))
    
    # Performance tracking
    triggered_at: Optional[datetime] = Field(default=None)
    triggered_price: Optional[float] = Field(default=None)
    max_profit: Optional[float] = Field(default=None)
    max_loss: Optional[float] = Field(default=None)
    final_pnl: Optional[float] = Field(default=None)
    
    # User interaction
    views: int = Field(default=0)
    likes: int = Field(default=0)
    
    def is_active(self) -> bool:
        """Check if signal is currently active"""
        if self.status != SignalStatus.ACTIVE:
            return False
        
        if self.expires_at and datetime.utcnow() > self.expires_at:
            return False
        
        return True
    
    def trigger(self, price: float) -> None:
        """Mark signal as triggered"""
        self.status = SignalStatus.TRIGGERED
        self.triggered_at = datetime.utcnow()
        self.triggered_price = price
        self.updated_at = datetime.utcnow()
    
    def expire(self) -> None:
        """Mark signal as expired"""
        self.status = SignalStatus.EXPIRED
        self.updated_at = datetime.utcnow()
    
    def calculate_pnl(self, current_price: float) -> float:
        """Calculate current P&L for the signal"""
        if not self.entry_price or not self.triggered_price:
            return 0.0
        
        if self.signal_type == SignalType.LONG:
            return ((current_price - self.triggered_price) / self.triggered_price) * 100
        elif self.signal_type == SignalType.SHORT:
            return ((self.triggered_price - current_price) / self.triggered_price) * 100
        
        return 0.0


class SignalCreate(SignalBase):
    """Signal creation model"""
    pass


class SignalUpdate(SQLModel):
    """Signal update model"""
    text_md: Optional[str] = None
    signal_type: Optional[SignalType] = None
    confidence: Optional[float] = None
    entry_price: Optional[float] = None
    stop_loss: Optional[float] = None
    take_profit: Optional[float] = None
    risk_level: Optional[str] = None
    position_size: Optional[float] = None
    status: Optional[SignalStatus] = None
    expires_at: Optional[datetime] = None
    indicators: Optional[dict] = None
    market_data: Optional[dict] = None


class SignalResponse(SignalBase):
    """Signal response model"""
    id: int
    status: SignalStatus
    created_at: datetime
    updated_at: datetime
    expires_at: Optional[datetime]
    analysis_id: Optional[int]
    llm_provider: Optional[str]
    llm_model: Optional[str]
    triggered_at: Optional[datetime]
    triggered_price: Optional[float]
    max_profit: Optional[float]
    max_loss: Optional[float]
    final_pnl: Optional[float]
    views: int
    likes: int
    
    # Computed fields
    is_active: bool
    current_pnl: Optional[float] = None
    
    @classmethod
    def from_signal(cls, signal: Signal, current_price: float = None) -> "SignalResponse":
        """Create response model from Signal instance"""
        current_pnl = None
        if current_price and signal.triggered_price:
            current_pnl = signal.calculate_pnl(current_price)
        
        return cls(
            id=signal.id,
            symbol=signal.symbol,
            tf=signal.tf,
            text_md=signal.text_md,
            ts=signal.ts,
            signal_type=signal.signal_type,
            confidence=signal.confidence,
            entry_price=signal.entry_price,
            stop_loss=signal.stop_loss,
            take_profit=signal.take_profit,
            risk_level=signal.risk_level,
            position_size=signal.position_size,
            status=signal.status,
            created_at=signal.created_at,
            updated_at=signal.updated_at,
            expires_at=signal.expires_at,
            analysis_id=signal.analysis_id,
            llm_provider=signal.llm_provider,
            llm_model=signal.llm_model,
            triggered_at=signal.triggered_at,
            triggered_price=signal.triggered_price,
            max_profit=signal.max_profit,
            max_loss=signal.max_loss,
            final_pnl=signal.final_pnl,
            views=signal.views,
            likes=signal.likes,
            is_active=signal.is_active(),
            current_pnl=current_pnl,
        )


class SignalList(SQLModel):
    """Signal list response model"""
    signals: List[SignalResponse]
    total: int
    page: int
    size: int
    pages: int


class SignalFilter(SQLModel):
    """Signal filter model"""
    symbol: Optional[str] = None
    tf: Optional[SignalTimeframe] = None
    signal_type: Optional[SignalType] = None
    status: Optional[SignalStatus] = None
    min_confidence: Optional[float] = Field(default=None, ge=0, le=100)
    risk_level: Optional[str] = None
    date_from: Optional[datetime] = None
    date_to: Optional[datetime] = None


class SignalStats(SQLModel):
    """Signal statistics model"""
    total_signals: int
    active_signals: int
    triggered_signals: int
    expired_signals: int
    avg_confidence: float
    success_rate: float
    avg_pnl: float
    best_signal_pnl: float
    worst_signal_pnl: float
    
    # By timeframe
    by_timeframe: dict
    
    # By symbol
    by_symbol: dict
    
    # By signal type
    by_type: dict
