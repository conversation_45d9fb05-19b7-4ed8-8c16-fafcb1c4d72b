"""
Subscription model based on ChartGenius_TZ/er.ddl.sql schema
"""

from datetime import datetime
from enum import Enum
from typing import Optional

from sqlmodel import SQLModel, Field, Column, JSON
from pydantic import validator


class SubscriptionTier(str, Enum):
    """Subscription tier enumeration"""
    FREE = "free"
    PREMIUM = "premium"
    VIP = "vip"


class SubscriptionSource(str, Enum):
    """Subscription source enumeration"""
    STRIPE = "stripe"
    TELEGRAM = "telegram"
    ADMIN = "admin"
    PROMO = "promo"


class SubscriptionStatus(str, Enum):
    """Subscription status enumeration"""
    ACTIVE = "active"
    EXPIRED = "expired"
    CANCELLED = "cancelled"
    PENDING = "pending"


class SubscriptionBase(SQLModel):
    """Base subscription model"""
    user_id: str = Field(index=True, description="User ID reference")
    tier: SubscriptionTier = Field(description="Subscription tier")
    price: float = Field(ge=0, description="Price paid")
    exp_ts: int = Field(description="Expiration timestamp")
    source: SubscriptionSource = Field(description="Payment source")
    
    # Payment details
    payment_id: Optional[str] = Field(default=None, description="External payment ID")
    currency: str = Field(default="USD", max_length=3)
    
    # Metadata
    metadata: Optional[dict] = Field(default=None, description="Additional metadata")
    
    @validator("price")
    def validate_price(cls, v):
        if v < 0:
            raise ValueError("Price cannot be negative")
        return v
    
    @validator("exp_ts")
    def validate_expiration(cls, v):
        import time
        if v < int(time.time()) - 86400:  # Allow 1 day in the past for processing
            raise ValueError("Expiration timestamp cannot be in the past")
        return v


class Subscription(SubscriptionBase, table=True):
    """Subscription database model"""
    __tablename__ = "subscriptions"
    
    id: Optional[int] = Field(default=None, primary_key=True)
    status: SubscriptionStatus = Field(default=SubscriptionStatus.ACTIVE)
    
    # Timestamps
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)
    activated_at: Optional[datetime] = Field(default=None)
    cancelled_at: Optional[datetime] = Field(default=None)
    
    # Payment tracking
    payment_data: Optional[dict] = Field(default=None, sa_column=Column(JSON))
    
    # Auto-renewal
    auto_renew: bool = Field(default=False)
    renewal_price: Optional[float] = Field(default=None)
    
    def is_active(self) -> bool:
        """Check if subscription is currently active"""
        import time
        return (
            self.status == SubscriptionStatus.ACTIVE and
            self.exp_ts > int(time.time())
        )
    
    def days_remaining(self) -> int:
        """Get days remaining in subscription"""
        import time
        if not self.is_active():
            return 0
        
        days = (self.exp_ts - int(time.time())) / (24 * 60 * 60)
        return max(0, int(days))
    
    def extend(self, days: int) -> None:
        """Extend subscription by specified days"""
        self.exp_ts += days * 24 * 60 * 60
        self.updated_at = datetime.utcnow()
    
    def cancel(self, reason: str = None) -> None:
        """Cancel subscription"""
        self.status = SubscriptionStatus.CANCELLED
        self.cancelled_at = datetime.utcnow()
        self.updated_at = datetime.utcnow()
        
        if reason and self.metadata:
            self.metadata["cancellation_reason"] = reason
        elif reason:
            self.metadata = {"cancellation_reason": reason}
    
    def activate(self) -> None:
        """Activate subscription"""
        self.status = SubscriptionStatus.ACTIVE
        self.activated_at = datetime.utcnow()
        self.updated_at = datetime.utcnow()


class SubscriptionCreate(SubscriptionBase):
    """Subscription creation model"""
    pass


class SubscriptionUpdate(SQLModel):
    """Subscription update model"""
    tier: Optional[SubscriptionTier] = None
    price: Optional[float] = None
    exp_ts: Optional[int] = None
    status: Optional[SubscriptionStatus] = None
    auto_renew: Optional[bool] = None
    renewal_price: Optional[float] = None
    metadata: Optional[dict] = None


class SubscriptionResponse(SubscriptionBase):
    """Subscription response model"""
    id: int
    status: SubscriptionStatus
    created_at: datetime
    updated_at: datetime
    activated_at: Optional[datetime]
    cancelled_at: Optional[datetime]
    auto_renew: bool
    renewal_price: Optional[float]
    
    # Computed fields
    is_active: bool
    days_remaining: int
    
    @classmethod
    def from_subscription(cls, subscription: Subscription) -> "SubscriptionResponse":
        """Create response model from Subscription instance"""
        return cls(
            id=subscription.id,
            user_id=subscription.user_id,
            tier=subscription.tier,
            price=subscription.price,
            exp_ts=subscription.exp_ts,
            source=subscription.source,
            payment_id=subscription.payment_id,
            currency=subscription.currency,
            metadata=subscription.metadata,
            status=subscription.status,
            created_at=subscription.created_at,
            updated_at=subscription.updated_at,
            activated_at=subscription.activated_at,
            cancelled_at=subscription.cancelled_at,
            auto_renew=subscription.auto_renew,
            renewal_price=subscription.renewal_price,
            is_active=subscription.is_active(),
            days_remaining=subscription.days_remaining(),
        )


class SubscriptionList(SQLModel):
    """Subscription list response model"""
    subscriptions: list[SubscriptionResponse]
    total: int
    page: int
    size: int
    pages: int


# Payment models
class PaymentRequest(SQLModel):
    """Payment request model"""
    tier: SubscriptionTier
    duration_months: int = Field(ge=1, le=12, default=1)
    currency: str = Field(default="USD", max_length=3)
    payment_method: str = Field(description="stripe or telegram")
    
    # Optional promo code
    promo_code: Optional[str] = Field(default=None, max_length=50)
    
    # Telegram-specific fields
    telegram_payment_charge_id: Optional[str] = Field(default=None)
    telegram_provider_payment_charge_id: Optional[str] = Field(default=None)


class PaymentResponse(SQLModel):
    """Payment response model"""
    payment_id: str
    amount: float
    currency: str
    status: str
    
    # Stripe-specific fields
    stripe_client_secret: Optional[str] = None
    stripe_payment_intent_id: Optional[str] = None
    
    # Telegram-specific fields
    telegram_invoice_payload: Optional[str] = None
    
    # Subscription details
    subscription_id: Optional[int] = None
    tier: SubscriptionTier
    expires_at: int


class PaymentWebhook(SQLModel):
    """Payment webhook model"""
    provider: str
    event_type: str
    payment_id: str
    status: str
    amount: Optional[float] = None
    currency: Optional[str] = None
    metadata: Optional[dict] = None
