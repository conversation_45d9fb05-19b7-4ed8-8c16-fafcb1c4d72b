"""
LLM Service for ChartGenius Backend v2
Unified interface for OpenAI and Gemini CLI with fallback mechanism
"""

import asyncio
import json
import subprocess
import time
from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Tuple, Any

import structlog
from openai import AsyncOpenAI
from pydantic import BaseModel

from app.core.config import get_settings
from app.core.exceptions import ExternalServiceError, RateLimitError
from app.middleware.rate_limit import RateLimitManager
from app.middleware.metrics import metrics

logger = structlog.get_logger(__name__)
settings = get_settings()


class LLMMessage(BaseModel):
    """LLM message model"""
    role: str  # "system", "user", "assistant"
    content: str


class LLMResponse(BaseModel):
    """LLM response model"""
    content: str
    provider: str
    model: str
    tokens_used: int
    duration_ms: float
    finish_reason: str
    metadata: Optional[Dict[str, Any]] = None


class LLMProvider(ABC):
    """Abstract base class for LLM providers"""
    
    @abstractmethod
    async def generate(
        self,
        messages: List[LLMMessage],
        max_tokens: int = 4000,
        temperature: float = 0.7,
        **kwargs
    ) -> LLMResponse:
        """Generate response from LLM"""
        pass
    
    @abstractmethod
    async def is_available(self) -> bool:
        """Check if provider is available"""
        pass
    
    @property
    @abstractmethod
    def name(self) -> str:
        """Provider name"""
        pass


class OpenAIProvider(LLMProvider):
    """OpenAI GPT provider"""
    
    def __init__(self):
        self.client = AsyncOpenAI(api_key=settings.OPENAI_API_KEY)
        self.model = settings.OPENAI_MODEL
    
    @property
    def name(self) -> str:
        return "openai"
    
    async def generate(
        self,
        messages: List[LLMMessage],
        max_tokens: int = 4000,
        temperature: float = 0.7,
        **kwargs
    ) -> LLMResponse:
        """Generate response using OpenAI API"""
        
        start_time = time.time()
        
        try:
            # Convert messages to OpenAI format
            openai_messages = [
                {"role": msg.role, "content": msg.content}
                for msg in messages
            ]
            
            # Make API call
            response = await self.client.chat.completions.create(
                model=self.model,
                messages=openai_messages,
                max_tokens=max_tokens,
                temperature=temperature,
                **kwargs
            )
            
            duration_ms = (time.time() - start_time) * 1000
            
            # Extract response data
            choice = response.choices[0]
            content = choice.message.content
            finish_reason = choice.finish_reason
            tokens_used = response.usage.total_tokens
            
            # Record metrics
            metrics.record_llm_request(
                provider=self.name,
                model=self.model,
                duration=duration_ms,
                tokens=tokens_used,
                status="success"
            )
            
            logger.info(
                "OpenAI request completed",
                model=self.model,
                tokens=tokens_used,
                duration_ms=duration_ms,
            )
            
            return LLMResponse(
                content=content,
                provider=self.name,
                model=self.model,
                tokens_used=tokens_used,
                duration_ms=duration_ms,
                finish_reason=finish_reason,
                metadata={
                    "usage": response.usage.model_dump(),
                    "model": response.model,
                }
            )
            
        except Exception as e:
            duration_ms = (time.time() - start_time) * 1000
            
            # Record error metrics
            metrics.record_llm_request(
                provider=self.name,
                model=self.model,
                duration=duration_ms,
                tokens=0,
                status="error"
            )
            
            logger.error(
                "OpenAI request failed",
                error=str(e),
                model=self.model,
                duration_ms=duration_ms,
                exc_info=True,
            )
            
            # Check for rate limiting
            if "rate_limit" in str(e).lower() or "429" in str(e):
                raise RateLimitError(f"OpenAI rate limit exceeded: {str(e)}")
            
            raise ExternalServiceError(
                f"OpenAI API error: {str(e)}",
                service_name="openai"
            )
    
    async def is_available(self) -> bool:
        """Check if OpenAI is available"""
        try:
            # Simple test request
            await self.client.chat.completions.create(
                model=self.model,
                messages=[{"role": "user", "content": "test"}],
                max_tokens=1,
            )
            return True
        except Exception as e:
            logger.warning("OpenAI availability check failed", error=str(e))
            return False


class GeminiCLIProvider(LLMProvider):
    """Gemini CLI provider"""
    
    def __init__(self):
        self.model = settings.GEMINI_MODEL
        self.config_path = settings.GEMINI_CLI_CONFIG
    
    @property
    def name(self) -> str:
        return "gemini"
    
    async def generate(
        self,
        messages: List[LLMMessage],
        max_tokens: int = 4000,
        temperature: float = 0.7,
        **kwargs
    ) -> LLMResponse:
        """Generate response using Gemini CLI"""
        
        start_time = time.time()
        
        try:
            # Check rate limits
            allowed, rate_info = await RateLimitManager.check_gemini_rate_limit("global")
            if not allowed:
                raise RateLimitError(
                    rate_info["error"],
                    details=rate_info,
                    retry_after=rate_info.get("retry_after", 60)
                )
            
            # Convert messages to Gemini format
            prompt = self._format_messages_for_gemini(messages)
            
            # Prepare CLI command
            cmd = [
                "gcli",
                "generate",
                "--model", self.model,
                "--max-tokens", str(max_tokens),
                "--temperature", str(temperature),
                "--config", self.config_path,
                "--format", "json",
                prompt
            ]
            
            # Execute CLI command
            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE,
            )
            
            stdout, stderr = await process.communicate()
            
            if process.returncode != 0:
                error_msg = stderr.decode() if stderr else "Unknown Gemini CLI error"
                raise ExternalServiceError(f"Gemini CLI error: {error_msg}")
            
            # Parse response
            response_data = json.loads(stdout.decode())
            content = response_data.get("content", "")
            tokens_used = response_data.get("tokens", 0)
            
            duration_ms = (time.time() - start_time) * 1000
            
            # Record metrics
            metrics.record_llm_request(
                provider=self.name,
                model=self.model,
                duration=duration_ms,
                tokens=tokens_used,
                status="success"
            )
            
            logger.info(
                "Gemini CLI request completed",
                model=self.model,
                tokens=tokens_used,
                duration_ms=duration_ms,
            )
            
            return LLMResponse(
                content=content,
                provider=self.name,
                model=self.model,
                tokens_used=tokens_used,
                duration_ms=duration_ms,
                finish_reason="stop",
                metadata=response_data,
            )
            
        except RateLimitError:
            raise
        except Exception as e:
            duration_ms = (time.time() - start_time) * 1000
            
            # Record error metrics
            metrics.record_llm_request(
                provider=self.name,
                model=self.model,
                duration=duration_ms,
                tokens=0,
                status="error"
            )
            
            logger.error(
                "Gemini CLI request failed",
                error=str(e),
                model=self.model,
                duration_ms=duration_ms,
                exc_info=True,
            )
            
            # Check for rate limiting
            if "quota" in str(e).lower() or "rate" in str(e).lower():
                raise RateLimitError(f"Gemini rate limit exceeded: {str(e)}")
            
            raise ExternalServiceError(
                f"Gemini CLI error: {str(e)}",
                service_name="gemini"
            )
    
    def _format_messages_for_gemini(self, messages: List[LLMMessage]) -> str:
        """Format messages for Gemini CLI"""
        formatted_parts = []
        
        for message in messages:
            if message.role == "system":
                formatted_parts.append(f"System: {message.content}")
            elif message.role == "user":
                formatted_parts.append(f"User: {message.content}")
            elif message.role == "assistant":
                formatted_parts.append(f"Assistant: {message.content}")
        
        return "\n\n".join(formatted_parts)
    
    async def is_available(self) -> bool:
        """Check if Gemini CLI is available"""
        try:
            # Check if CLI is installed and configured
            process = await asyncio.create_subprocess_exec(
                "gcli", "version",
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE,
            )
            
            stdout, stderr = await process.communicate()
            return process.returncode == 0
            
        except Exception as e:
            logger.warning("Gemini CLI availability check failed", error=str(e))
            return False


class LLMRouter:
    """LLM router with fallback mechanism"""
    
    def __init__(self):
        self.providers = {
            "openai": OpenAIProvider(),
            "gemini": GeminiCLIProvider(),
        }
        self.primary_provider = "gemini"  # Prefer Gemini as per ТЗ
        self.fallback_provider = "openai"
    
    async def generate(
        self,
        messages: List[LLMMessage],
        max_tokens: int = 4000,
        temperature: float = 0.7,
        preferred_provider: Optional[str] = None,
        **kwargs
    ) -> LLMResponse:
        """Generate response with fallback mechanism"""
        
        # Determine provider order
        if preferred_provider and preferred_provider in self.providers:
            provider_order = [preferred_provider]
            # Add other providers as fallbacks
            for name in [self.primary_provider, self.fallback_provider]:
                if name != preferred_provider and name not in provider_order:
                    provider_order.append(name)
        else:
            provider_order = [self.primary_provider, self.fallback_provider]
        
        last_error = None
        
        for provider_name in provider_order:
            provider = self.providers[provider_name]
            
            try:
                # Check if provider is available
                if not await provider.is_available():
                    logger.warning(f"{provider_name} provider not available, trying next")
                    continue
                
                # Try to generate response
                response = await provider.generate(
                    messages=messages,
                    max_tokens=max_tokens,
                    temperature=temperature,
                    **kwargs
                )
                
                logger.info(
                    "LLM request successful",
                    provider=provider_name,
                    tokens=response.tokens_used,
                    duration_ms=response.duration_ms,
                )
                
                return response
                
            except RateLimitError as e:
                logger.warning(
                    f"{provider_name} rate limited, trying fallback",
                    error=str(e),
                )
                last_error = e
                continue
                
            except ExternalServiceError as e:
                logger.warning(
                    f"{provider_name} service error, trying fallback",
                    error=str(e),
                )
                last_error = e
                continue
                
            except Exception as e:
                logger.error(
                    f"{provider_name} unexpected error",
                    error=str(e),
                    exc_info=True,
                )
                last_error = e
                continue
        
        # All providers failed
        if last_error:
            raise last_error
        else:
            raise ExternalServiceError("All LLM providers are unavailable")
    
    async def get_provider_status(self) -> Dict[str, bool]:
        """Get status of all providers"""
        status = {}
        
        for name, provider in self.providers.items():
            try:
                status[name] = await provider.is_available()
            except Exception:
                status[name] = False
        
        return status


# Global LLM router instance
llm_router = LLMRouter()
