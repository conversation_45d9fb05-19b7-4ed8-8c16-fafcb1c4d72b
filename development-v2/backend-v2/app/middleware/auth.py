"""
Authentication middleware for ChartGenius Backend v2
JWT RS256 authentication with Telegram WebApp support
"""

import time
from typing import Optional

import structlog
from fastapi import Request, Response
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from jose import J<PERSON><PERSON><PERSON>r, jwt
from starlette.middleware.base import BaseHTTPMiddleware

from app.core.config import get_settings
from app.core.exceptions import AuthenticationError
from app.models.user import User, UserRole
from app.core.database import get_async_session

logger = structlog.get_logger(__name__)
settings = get_settings()

# Security scheme for OpenAPI docs
security = HTTPBearer(auto_error=False)


class AuthMiddleware(BaseHTTPMiddleware):
    """Authentication middleware"""
    
    # Routes that don't require authentication
    PUBLIC_ROUTES = {
        "/",
        "/health",
        "/metrics",
        "/docs",
        "/redoc",
        "/openapi.json",
        "/api/v2/auth/login",
        "/api/v2/auth/telegram",
        "/api/v2/webhooks/stripe",
        "/api/v2/webhooks/telegram",
    }
    
    # Routes that require admin access
    ADMIN_ROUTES = {
        "/api/v2/admin/",
    }
    
    async def dispatch(self, request: Request, call_next):
        """Process authentication for each request"""
        
        # Skip authentication for public routes
        if self._is_public_route(request.url.path):
            return await call_next(request)
        
        # Extract and validate JWT token
        try:
            user = await self._authenticate_request(request)
            
            if user:
                # Add user to request state
                request.state.user = user
                request.state.user_id = user.tg_id
                
                # Check admin access for admin routes
                if self._is_admin_route(request.url.path):
                    if user.role != UserRole.ADMIN:
                        raise AuthenticationError("Admin access required")
                
                logger.info(
                    "User authenticated",
                    user_id=user.tg_id,
                    role=user.role,
                    path=request.url.path,
                )
            else:
                # No authentication required for this route
                pass
                
        except AuthenticationError as e:
            logger.warning(
                "Authentication failed",
                error=str(e),
                path=request.url.path,
                ip=request.client.host if request.client else None,
            )
            
            from fastapi.responses import JSONResponse
            return JSONResponse(
                status_code=401,
                content={
                    "error": "AUTHENTICATION_ERROR",
                    "message": str(e),
                    "timestamp": str(int(time.time())),
                }
            )
        
        except Exception as e:
            logger.error(
                "Authentication middleware error",
                error=str(e),
                path=request.url.path,
                exc_info=True,
            )
            
            from fastapi.responses import JSONResponse
            return JSONResponse(
                status_code=500,
                content={
                    "error": "INTERNAL_SERVER_ERROR",
                    "message": "Authentication error",
                    "timestamp": str(int(time.time())),
                }
            )
        
        # Continue to next middleware/route
        response = await call_next(request)
        return response
    
    def _is_public_route(self, path: str) -> bool:
        """Check if route is public"""
        # Exact match
        if path in self.PUBLIC_ROUTES:
            return True
        
        # Prefix match for webhooks and docs
        public_prefixes = ["/api/v2/webhooks/", "/docs", "/redoc"]
        return any(path.startswith(prefix) for prefix in public_prefixes)
    
    def _is_admin_route(self, path: str) -> bool:
        """Check if route requires admin access"""
        return any(path.startswith(route) for route in self.ADMIN_ROUTES)
    
    async def _authenticate_request(self, request: Request) -> Optional[User]:
        """Authenticate request and return user"""
        
        # Try to get token from Authorization header
        token = self._extract_token(request)
        
        if not token:
            # Check if authentication is required for this route
            if self._requires_auth(request.url.path):
                raise AuthenticationError("Missing authentication token")
            return None
        
        # Validate JWT token
        try:
            payload = jwt.decode(
                token,
                settings.JWT_PUBLIC_KEY,
                algorithms=[settings.JWT_ALGORITHM],
            )
            
            # Check token expiration
            if payload.get("exp", 0) < time.time():
                raise AuthenticationError("Token expired")
            
            # Get user ID from token
            user_id = payload.get("telegram_id")
            if not user_id:
                raise AuthenticationError("Invalid token payload")
            
            # Load user from database
            user = await self._load_user(user_id)
            if not user:
                raise AuthenticationError("User not found")
            
            # Check if user is active
            if not user.is_active:
                raise AuthenticationError("User account is disabled")
            
            if user.is_banned:
                raise AuthenticationError(f"User account is banned: {user.ban_reason}")
            
            return user
            
        except JWTError as e:
            raise AuthenticationError(f"Invalid token: {str(e)}")
    
    def _extract_token(self, request: Request) -> Optional[str]:
        """Extract JWT token from request"""
        
        # Try Authorization header
        auth_header = request.headers.get("Authorization")
        if auth_header and auth_header.startswith("Bearer "):
            return auth_header[7:]  # Remove "Bearer " prefix
        
        # Try query parameter (for WebSocket connections)
        token = request.query_params.get("token")
        if token:
            return token
        
        # Try cookie (for browser requests)
        token = request.cookies.get("access_token")
        if token:
            return token
        
        return None
    
    def _requires_auth(self, path: str) -> bool:
        """Check if path requires authentication"""
        # Most API routes require authentication
        if path.startswith("/api/v2/"):
            return not self._is_public_route(path)
        
        return False
    
    async def _load_user(self, user_id: str) -> Optional[User]:
        """Load user from database"""
        try:
            async for session in get_async_session():
                from sqlmodel import select
                
                statement = select(User).where(User.tg_id == user_id)
                result = await session.exec(statement)
                user = result.first()
                
                if user:
                    # Update last login
                    user.update_last_login()
                    session.add(user)
                    await session.commit()
                
                return user
                
        except Exception as e:
            logger.error("Failed to load user", user_id=user_id, error=str(e))
            return None


# JWT utilities
class JWTManager:
    """JWT token management utilities"""
    
    @staticmethod
    def create_access_token(user: User, expires_delta: int = None) -> str:
        """Create JWT access token"""
        if expires_delta is None:
            expires_delta = settings.JWT_EXPIRE_MINUTES * 60
        
        expire = int(time.time()) + expires_delta
        
        payload = {
            "telegram_id": user.tg_id,
            "role": user.role.value,
            "tier": user.tier.value,
            "exp": expire,
            "iat": int(time.time()),
            "iss": "chartgenius",
            "sub": user.tg_id,
        }
        
        token = jwt.encode(
            payload,
            settings.JWT_PRIVATE_KEY,
            algorithm=settings.JWT_ALGORITHM,
        )
        
        return token
    
    @staticmethod
    def decode_token(token: str) -> dict:
        """Decode JWT token"""
        try:
            payload = jwt.decode(
                token,
                settings.JWT_PUBLIC_KEY,
                algorithms=[settings.JWT_ALGORITHM],
            )
            return payload
        except JWTError as e:
            raise AuthenticationError(f"Invalid token: {str(e)}")
    
    @staticmethod
    def verify_telegram_webapp_data(init_data: str, bot_token: str) -> dict:
        """Verify Telegram WebApp init data"""
        import hashlib
        import hmac
        from urllib.parse import parse_qsl, unquote
        
        try:
            # Parse init data
            parsed_data = dict(parse_qsl(init_data))
            
            # Extract hash
            received_hash = parsed_data.pop("hash", "")
            
            # Create data check string
            data_check_arr = []
            for key, value in sorted(parsed_data.items()):
                data_check_arr.append(f"{key}={value}")
            data_check_string = "\n".join(data_check_arr)
            
            # Create secret key
            secret_key = hmac.new(
                "WebAppData".encode(),
                bot_token.encode(),
                hashlib.sha256
            ).digest()
            
            # Calculate hash
            calculated_hash = hmac.new(
                secret_key,
                data_check_string.encode(),
                hashlib.sha256
            ).hexdigest()
            
            # Verify hash
            if not hmac.compare_digest(received_hash, calculated_hash):
                raise AuthenticationError("Invalid Telegram WebApp data")
            
            # Check auth date (should be recent)
            auth_date = int(parsed_data.get("auth_date", 0))
            if abs(int(time.time()) - auth_date) > 86400:  # 24 hours
                raise AuthenticationError("Telegram WebApp data is too old")
            
            # Parse user data
            user_data = {}
            if "user" in parsed_data:
                import json
                user_data = json.loads(unquote(parsed_data["user"]))
            
            return {
                "user": user_data,
                "auth_date": auth_date,
                "query_id": parsed_data.get("query_id"),
                "chat_instance": parsed_data.get("chat_instance"),
                "chat_type": parsed_data.get("chat_type"),
            }
            
        except Exception as e:
            logger.error("Telegram WebApp verification failed", error=str(e))
            raise AuthenticationError("Invalid Telegram WebApp data")


# Dependency for route handlers
async def get_current_user(request: Request) -> User:
    """Get current authenticated user"""
    user = getattr(request.state, "user", None)
    if not user:
        raise AuthenticationError("Authentication required")
    return user


async def get_current_admin_user(request: Request) -> User:
    """Get current authenticated admin user"""
    user = await get_current_user(request)
    if user.role != UserRole.ADMIN:
        raise AuthenticationError("Admin access required")
    return user


# Optional authentication dependency
async def get_optional_user(request: Request) -> Optional[User]:
    """Get current user if authenticated, None otherwise"""
    return getattr(request.state, "user", None)
