"""
Rate limiting middleware for ChartGenius Backend v2
Redis-based rate limiting with different limits per endpoint and user tier
"""

import time
from typing import Dict, Optional, Tuple

import structlog
from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware

from app.core.config import get_settings
from app.core.redis import rate_limiter
from app.core.exceptions import RateLimitError
from app.models.user import UserRole, UserTier

logger = structlog.get_logger(__name__)
settings = get_settings()


class RateLimitMiddleware(BaseHTTPMiddleware):
    """Rate limiting middleware"""
    
    # Rate limits by endpoint pattern and user tier
    RATE_LIMITS = {
        # Authentication endpoints
        "/api/v2/auth/login": {
            "default": (10, 300),  # 10 requests per 5 minutes
            "per_ip": (20, 300),   # 20 requests per IP per 5 minutes
        },
        "/api/v2/auth/telegram": {
            "default": (10, 300),
            "per_ip": (20, 300),
        },
        
        # LLM endpoints (most restrictive)
        "/api/v2/llm/chat": {
            UserTier.FREE: (5, 3600),      # 5 requests per hour
            UserTier.PREMIUM: (50, 3600),  # 50 requests per hour
            UserTier.VIP: (200, 3600),     # 200 requests per hour
            "admin": (1000, 3600),         # 1000 requests per hour for admin
        },
        "/api/v2/analysis/create": {
            UserTier.FREE: (3, 3600),      # 3 analyses per hour
            UserTier.PREMIUM: (20, 3600),  # 20 analyses per hour
            UserTier.VIP: (100, 3600),     # 100 analyses per hour
            "admin": (500, 3600),
        },
        
        # Signal endpoints
        "/api/v2/signals": {
            UserTier.FREE: (10, 60),       # 10 requests per minute
            UserTier.PREMIUM: (30, 60),    # 30 requests per minute
            UserTier.VIP: (100, 60),       # 100 requests per minute
            "admin": (200, 60),
        },
        
        # User management
        "/api/v2/users": {
            "default": (30, 60),           # 30 requests per minute
            "admin": (100, 60),
        },
        
        # Payment endpoints
        "/api/v2/payments": {
            "default": (5, 300),           # 5 requests per 5 minutes
            "per_ip": (10, 300),
        },
        
        # Admin endpoints
        "/api/v2/admin": {
            "admin": (100, 60),            # 100 requests per minute for admin
        },
        
        # Default for all other API endpoints
        "default_api": {
            UserTier.FREE: (60, 60),       # 60 requests per minute
            UserTier.PREMIUM: (120, 60),   # 120 requests per minute
            UserTier.VIP: (300, 60),       # 300 requests per minute
            "admin": (500, 60),
        },
    }
    
    # Endpoints that are exempt from rate limiting
    EXEMPT_ROUTES = {
        "/",
        "/health",
        "/metrics",
        "/docs",
        "/redoc",
        "/openapi.json",
    }
    
    async def dispatch(self, request: Request, call_next):
        """Apply rate limiting to requests"""
        
        # Skip rate limiting for exempt routes
        if request.url.path in self.EXEMPT_ROUTES:
            return await call_next(request)
        
        try:
            # Check rate limits
            await self._check_rate_limits(request)
            
            # Continue to next middleware/route
            response = await call_next(request)
            
            # Add rate limit headers to response
            self._add_rate_limit_headers(request, response)
            
            return response
            
        except RateLimitError as e:
            logger.warning(
                "Rate limit exceeded",
                path=request.url.path,
                user_id=getattr(request.state, "user_id", None),
                ip=request.client.host if request.client else None,
                error=str(e),
            )
            
            from fastapi.responses import JSONResponse
            return JSONResponse(
                status_code=429,
                content={
                    "error": "RATE_LIMIT_ERROR",
                    "message": str(e),
                    "details": e.details,
                    "timestamp": str(int(time.time())),
                },
                headers={
                    "Retry-After": str(e.details.get("retry_after", 60)),
                    "X-RateLimit-Limit": str(e.details.get("limit", 0)),
                    "X-RateLimit-Remaining": "0",
                    "X-RateLimit-Reset": str(int(time.time()) + e.details.get("retry_after", 60)),
                }
            )
        
        except Exception as e:
            logger.error(
                "Rate limiting middleware error",
                error=str(e),
                path=request.url.path,
                exc_info=True,
            )
            
            # Continue without rate limiting if Redis is down
            return await call_next(request)
    
    async def _check_rate_limits(self, request: Request) -> None:
        """Check rate limits for the request"""
        
        # Get rate limit configuration for this endpoint
        limits = self._get_rate_limits(request)
        
        if not limits:
            return  # No rate limiting configured
        
        # Check each rate limit
        for identifier, (limit, window) in limits.items():
            allowed, info = await rate_limiter.is_allowed(
                identifier=identifier,
                limit=limit,
                window_seconds=window,
                cost=1,
            )
            
            if not allowed:
                raise RateLimitError(
                    message=f"Rate limit exceeded for {identifier}",
                    details={
                        "limit": limit,
                        "window": window,
                        "retry_after": info.get("reset_time", window),
                        "identifier": identifier,
                    },
                    retry_after=info.get("reset_time", window),
                )
            
            # Store rate limit info for headers
            request.state.rate_limit_info = info
    
    def _get_rate_limits(self, request: Request) -> Dict[str, Tuple[int, int]]:
        """Get rate limits for the request"""
        path = request.url.path
        user = getattr(request.state, "user", None)
        ip = request.client.host if request.client else "unknown"
        
        # Find matching rate limit configuration
        config = None
        
        # Check exact path matches first
        for pattern, limits in self.RATE_LIMITS.items():
            if pattern == path:
                config = limits
                break
        
        # Check prefix matches
        if not config:
            for pattern, limits in self.RATE_LIMITS.items():
                if path.startswith(pattern):
                    config = limits
                    break
        
        # Use default API limits if no specific config found
        if not config and path.startswith("/api/v2/"):
            config = self.RATE_LIMITS["default_api"]
        
        if not config:
            return {}  # No rate limiting
        
        # Build rate limit identifiers
        limits = {}
        
        # Per-user rate limits
        if user:
            user_tier = user.tier
            user_role = user.role
            
            # Check for admin limits
            if user_role == UserRole.ADMIN and "admin" in config:
                limit, window = config["admin"]
                limits[f"user:{user.tg_id}"] = (limit, window)
            
            # Check for tier-specific limits
            elif user_tier in config:
                limit, window = config[user_tier]
                limits[f"user:{user.tg_id}"] = (limit, window)
            
            # Check for default user limits
            elif "default" in config:
                limit, window = config["default"]
                limits[f"user:{user.tg_id}"] = (limit, window)
        
        # Per-IP rate limits (for unauthenticated requests or additional protection)
        if "per_ip" in config:
            limit, window = config["per_ip"]
            limits[f"ip:{ip}"] = (limit, window)
        
        # Global rate limits
        if "global" in config:
            limit, window = config["global"]
            limits["global"] = (limit, window)
        
        # Default limits for unauthenticated requests
        if not user and not limits and "default" in config:
            limit, window = config["default"]
            limits[f"ip:{ip}"] = (limit, window)
        
        return limits
    
    def _add_rate_limit_headers(self, request: Request, response: Response) -> None:
        """Add rate limit headers to response"""
        rate_limit_info = getattr(request.state, "rate_limit_info", None)
        
        if rate_limit_info:
            response.headers["X-RateLimit-Limit"] = str(rate_limit_info.get("limit", 0))
            response.headers["X-RateLimit-Remaining"] = str(rate_limit_info.get("remaining", 0))
            response.headers["X-RateLimit-Reset"] = str(
                int(time.time()) + rate_limit_info.get("reset_time", 60)
            )


# Rate limiting utilities
class RateLimitManager:
    """Rate limiting management utilities"""
    
    @staticmethod
    async def check_gemini_rate_limit(user_id: str) -> Tuple[bool, Dict]:
        """Check Gemini API rate limits"""
        # Check per-minute limit
        allowed_rpm, info_rpm = await rate_limiter.is_allowed(
            identifier=f"gemini:rpm:{user_id}",
            limit=settings.GEMINI_RATE_LIMIT_RPM,
            window_seconds=60,
        )
        
        if not allowed_rpm:
            return False, {
                "error": "Gemini RPM limit exceeded",
                "limit": settings.GEMINI_RATE_LIMIT_RPM,
                "window": "1 minute",
                "retry_after": info_rpm.get("reset_time", 60),
            }
        
        # Check per-day limit
        allowed_rpd, info_rpd = await rate_limiter.is_allowed(
            identifier=f"gemini:rpd:{user_id}",
            limit=settings.GEMINI_RATE_LIMIT_RPD,
            window_seconds=86400,  # 24 hours
        )
        
        if not allowed_rpd:
            return False, {
                "error": "Gemini RPD limit exceeded",
                "limit": settings.GEMINI_RATE_LIMIT_RPD,
                "window": "24 hours",
                "retry_after": info_rpd.get("reset_time", 3600),
            }
        
        return True, {
            "rpm_remaining": info_rpm.get("remaining", 0),
            "rpd_remaining": info_rpd.get("remaining", 0),
        }
    
    @staticmethod
    async def reset_user_rate_limits(user_id: str) -> bool:
        """Reset all rate limits for a user (admin function)"""
        try:
            # Reset common rate limit patterns
            patterns = [
                f"user:{user_id}",
                f"gemini:rpm:{user_id}",
                f"gemini:rpd:{user_id}",
            ]
            
            for pattern in patterns:
                await rate_limiter.reset(pattern, 3600)  # Reset with 1 hour window
            
            logger.info("Rate limits reset for user", user_id=user_id)
            return True
            
        except Exception as e:
            logger.error("Failed to reset rate limits", user_id=user_id, error=str(e))
            return False
    
    @staticmethod
    async def get_rate_limit_status(user_id: str) -> Dict:
        """Get current rate limit status for a user"""
        try:
            from app.core.redis import get_redis
            
            redis = await get_redis()
            
            # Get current counts for various limits
            status = {}
            
            # Check common rate limit keys
            keys = [
                f"ratelimit:user:{user_id}:*",
                f"ratelimit:gemini:rpm:{user_id}:*",
                f"ratelimit:gemini:rpd:{user_id}:*",
            ]
            
            for pattern in keys:
                matching_keys = await redis.keys(pattern)
                for key in matching_keys:
                    count = await redis.get(key)
                    ttl = await redis.ttl(key)
                    
                    status[key] = {
                        "count": int(count) if count else 0,
                        "ttl": ttl,
                    }
            
            return status
            
        except Exception as e:
            logger.error("Failed to get rate limit status", user_id=user_id, error=str(e))
            return {}
