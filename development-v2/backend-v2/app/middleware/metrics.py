"""
Metrics middleware for ChartGenius Backend v2
Prometheus metrics collection
"""

import time
from typing import Dict

import structlog
from fastapi import Request, Response
from prometheus_client import Counter, Histogram, Gauge
from starlette.middleware.base import BaseHTTPMiddleware

logger = structlog.get_logger(__name__)

# Prometheus metrics
REQUEST_COUNT = Counter(
    "http_requests_total",
    "Total HTTP requests",
    ["method", "endpoint", "status_code", "user_tier"]
)

REQUEST_DURATION = Histogram(
    "http_request_duration_seconds",
    "HTTP request duration in seconds",
    ["method", "endpoint", "status_code"],
    buckets=[0.01, 0.05, 0.1, 0.25, 0.5, 1.0, 2.5, 5.0, 10.0]
)

ACTIVE_REQUESTS = Gauge(
    "http_requests_active",
    "Number of active HTTP requests"
)

LLM_REQUESTS = Counter(
    "llm_requests_total",
    "Total LLM requests",
    ["provider", "model", "status"]
)

LLM_DURATION = Histogram(
    "llm_request_duration_seconds",
    "LLM request duration in seconds",
    ["provider", "model"],
    buckets=[0.1, 0.5, 1.0, 2.0, 5.0, 10.0, 30.0, 60.0]
)

LLM_TOKENS = Histogram(
    "llm_tokens_total",
    "LLM tokens used",
    ["provider", "model", "type"],
    buckets=[10, 50, 100, 500, 1000, 2000, 4000, 8000]
)

DATABASE_QUERIES = Counter(
    "database_queries_total",
    "Total database queries",
    ["operation", "table", "status"]
)

DATABASE_DURATION = Histogram(
    "database_query_duration_seconds",
    "Database query duration in seconds",
    ["operation", "table"],
    buckets=[0.001, 0.005, 0.01, 0.05, 0.1, 0.5, 1.0, 5.0]
)

REDIS_OPERATIONS = Counter(
    "redis_operations_total",
    "Total Redis operations",
    ["operation", "status"]
)

USER_ACTIONS = Counter(
    "user_actions_total",
    "Total user actions",
    ["action", "user_tier", "status"]
)

PAYMENT_TRANSACTIONS = Counter(
    "payment_transactions_total",
    "Total payment transactions",
    ["provider", "tier", "status"]
)

SIGNAL_GENERATION = Counter(
    "signals_generated_total",
    "Total signals generated",
    ["symbol", "timeframe", "signal_type"]
)

ERROR_COUNT = Counter(
    "errors_total",
    "Total errors",
    ["error_type", "endpoint", "severity"]
)


class MetricsMiddleware(BaseHTTPMiddleware):
    """Metrics collection middleware"""
    
    async def dispatch(self, request: Request, call_next):
        """Collect metrics for each request"""
        
        # Skip metrics collection for metrics endpoint itself
        if request.url.path == "/metrics":
            return await call_next(request)
        
        start_time = time.time()
        
        # Increment active requests
        ACTIVE_REQUESTS.inc()
        
        try:
            # Process request
            response = await call_next(request)
            
            # Calculate duration
            duration = time.time() - start_time
            
            # Get metrics labels
            method = request.method
            endpoint = self._normalize_endpoint(request.url.path)
            status_code = str(response.status_code)
            user_tier = self._get_user_tier(request)
            
            # Record metrics
            REQUEST_COUNT.labels(
                method=method,
                endpoint=endpoint,
                status_code=status_code,
                user_tier=user_tier
            ).inc()
            
            REQUEST_DURATION.labels(
                method=method,
                endpoint=endpoint,
                status_code=status_code
            ).observe(duration)
            
            # Log slow requests
            if duration > 5.0:
                logger.warning(
                    "Slow request detected",
                    method=method,
                    endpoint=endpoint,
                    duration=duration,
                    status_code=status_code,
                )
            
            return response
            
        except Exception as e:
            # Record error metrics
            duration = time.time() - start_time
            endpoint = self._normalize_endpoint(request.url.path)
            
            ERROR_COUNT.labels(
                error_type=type(e).__name__,
                endpoint=endpoint,
                severity="error"
            ).inc()
            
            REQUEST_COUNT.labels(
                method=request.method,
                endpoint=endpoint,
                status_code="500",
                user_tier=self._get_user_tier(request)
            ).inc()
            
            REQUEST_DURATION.labels(
                method=request.method,
                endpoint=endpoint,
                status_code="500"
            ).observe(duration)
            
            raise
            
        finally:
            # Decrement active requests
            ACTIVE_REQUESTS.dec()
    
    def _normalize_endpoint(self, path: str) -> str:
        """Normalize endpoint path for metrics"""
        # Replace dynamic path parameters with placeholders
        import re
        
        # Common patterns
        patterns = [
            (r'/api/v2/users/\d+', '/api/v2/users/{id}'),
            (r'/api/v2/signals/\d+', '/api/v2/signals/{id}'),
            (r'/api/v2/subscriptions/\d+', '/api/v2/subscriptions/{id}'),
            (r'/api/v2/analysis/\d+', '/api/v2/analysis/{id}'),
            (r'/api/v2/admin/users/\d+', '/api/v2/admin/users/{id}'),
        ]
        
        normalized = path
        for pattern, replacement in patterns:
            normalized = re.sub(pattern, replacement, normalized)
        
        return normalized
    
    def _get_user_tier(self, request: Request) -> str:
        """Get user tier for metrics"""
        user = getattr(request.state, "user", None)
        if user:
            return user.tier.value
        return "anonymous"


# Metrics utilities
class MetricsCollector:
    """Utility class for collecting custom metrics"""
    
    @staticmethod
    def record_llm_request(provider: str, model: str, duration: float, tokens: int, status: str):
        """Record LLM request metrics"""
        LLM_REQUESTS.labels(provider=provider, model=model, status=status).inc()
        LLM_DURATION.labels(provider=provider, model=model).observe(duration)
        
        if tokens > 0:
            LLM_TOKENS.labels(provider=provider, model=model, type="total").observe(tokens)
    
    @staticmethod
    def record_database_query(operation: str, table: str, duration: float, status: str):
        """Record database query metrics"""
        DATABASE_QUERIES.labels(operation=operation, table=table, status=status).inc()
        DATABASE_DURATION.labels(operation=operation, table=table).observe(duration)
    
    @staticmethod
    def record_redis_operation(operation: str, status: str):
        """Record Redis operation metrics"""
        REDIS_OPERATIONS.labels(operation=operation, status=status).inc()
    
    @staticmethod
    def record_user_action(action: str, user_tier: str, status: str):
        """Record user action metrics"""
        USER_ACTIONS.labels(action=action, user_tier=user_tier, status=status).inc()
    
    @staticmethod
    def record_payment_transaction(provider: str, tier: str, status: str):
        """Record payment transaction metrics"""
        PAYMENT_TRANSACTIONS.labels(provider=provider, tier=tier, status=status).inc()
    
    @staticmethod
    def record_signal_generation(symbol: str, timeframe: str, signal_type: str):
        """Record signal generation metrics"""
        SIGNAL_GENERATION.labels(
            symbol=symbol,
            timeframe=timeframe,
            signal_type=signal_type
        ).inc()
    
    @staticmethod
    def record_error(error_type: str, endpoint: str, severity: str = "error"):
        """Record error metrics"""
        ERROR_COUNT.labels(
            error_type=error_type,
            endpoint=endpoint,
            severity=severity
        ).inc()


# Global metrics collector instance
metrics = MetricsCollector()


# Custom metrics for business logic
SUBSCRIPTION_ACTIVE = Gauge(
    "subscriptions_active_total",
    "Number of active subscriptions",
    ["tier"]
)

ANALYSIS_QUEUE_SIZE = Gauge(
    "analysis_queue_size",
    "Number of analyses in queue"
)

SIGNAL_ACCURACY = Gauge(
    "signal_accuracy_percentage",
    "Signal accuracy percentage",
    ["timeframe", "symbol"]
)

REVENUE_TOTAL = Counter(
    "revenue_total_usd",
    "Total revenue in USD",
    ["tier", "provider"]
)


class BusinessMetricsCollector:
    """Collector for business-specific metrics"""
    
    @staticmethod
    def update_active_subscriptions(tier_counts: Dict[str, int]):
        """Update active subscription counts"""
        for tier, count in tier_counts.items():
            SUBSCRIPTION_ACTIVE.labels(tier=tier).set(count)
    
    @staticmethod
    def update_analysis_queue_size(size: int):
        """Update analysis queue size"""
        ANALYSIS_QUEUE_SIZE.set(size)
    
    @staticmethod
    def update_signal_accuracy(timeframe: str, symbol: str, accuracy: float):
        """Update signal accuracy"""
        SIGNAL_ACCURACY.labels(timeframe=timeframe, symbol=symbol).set(accuracy)
    
    @staticmethod
    def record_revenue(amount: float, tier: str, provider: str):
        """Record revenue"""
        REVENUE_TOTAL.labels(tier=tier, provider=provider).inc(amount)


# Global business metrics collector
business_metrics = BusinessMetricsCollector()
