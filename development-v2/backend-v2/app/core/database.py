"""
Database configuration for Oracle Autonomous JSON Database
Based on ChartGenius_TZ/er.ddl.sql schema
"""

import asyncio
from typing import AsyncGenerator, Optional

import structlog
from sqlalchemy import create_engine, text
from sqlalchemy.ext.asyncio import AsyncSession, async_sessionmaker, create_async_engine
from sqlalchemy.orm import DeclarativeBase, sessionmaker
from sqlmodel import SQLModel

from app.core.config import get_settings

logger = structlog.get_logger(__name__)
settings = get_settings()

# Global database engine and session maker
async_engine = None
async_session_maker = None
sync_engine = None
sync_session_maker = None


class Base(DeclarativeBase):
    """Base class for all database models"""
    pass


async def init_db() -> None:
    """Initialize database connection and create tables"""
    global async_engine, async_session_maker, sync_engine, sync_session_maker
    
    try:
        # Create async engine for main operations
        if settings.DATABASE_URL.startswith("sqlite"):
            # SQLite configuration
            async_engine = create_async_engine(
                settings.DATABASE_URL,
                echo=settings.DEBUG,  # Log SQL queries in debug mode
                connect_args={"check_same_thread": False}
            )
        else:
            # PostgreSQL/Oracle configuration
            async_engine = create_async_engine(
                settings.DATABASE_URL,
                pool_size=settings.DATABASE_POOL_SIZE,
                max_overflow=settings.DATABASE_MAX_OVERFLOW,
                pool_pre_ping=True,
                pool_recycle=3600,  # Recycle connections every hour
                echo=settings.DEBUG,  # Log SQL queries in debug mode
            )
        
        # Create async session maker
        async_session_maker = async_sessionmaker(
            async_engine,
            class_=AsyncSession,
            expire_on_commit=False,
        )
        
        # Create sync engine for migrations and admin tasks
        sync_database_url = settings.DATABASE_URL.replace("+asyncpg", "")
        sync_engine = create_engine(
            sync_database_url,
            pool_size=5,
            max_overflow=10,
            pool_pre_ping=True,
            pool_recycle=3600,
            echo=settings.DEBUG,
        )
        
        # Create sync session maker
        sync_session_maker = sessionmaker(
            sync_engine,
            expire_on_commit=False,
        )
        
        # Test connection
        async with async_engine.begin() as conn:
            await conn.execute(text("SELECT 1"))
        
        logger.info("Database connection established", database_url=settings.DATABASE_URL)
        
        # Create tables if they don't exist
        await create_tables()
        
    except Exception as e:
        logger.error("Failed to initialize database", error=str(e), exc_info=True)
        raise


async def close_db() -> None:
    """Close database connections"""
    global async_engine, sync_engine
    
    if async_engine:
        await async_engine.dispose()
        logger.info("Async database engine disposed")
    
    if sync_engine:
        sync_engine.dispose()
        logger.info("Sync database engine disposed")


async def create_tables() -> None:
    """Create database tables based on SQLModel definitions"""
    try:
        async with async_engine.begin() as conn:
            # Import all models to ensure they're registered
            from app.models.user import User
            from app.models.subscription import Subscription
            from app.models.analysis import Analysis
            from app.models.signal import Signal
            from app.models.watchlist import Watchlist
            
            # Create all tables
            await conn.run_sync(SQLModel.metadata.create_all)
            
        logger.info("Database tables created/verified")
        
    except Exception as e:
        logger.error("Failed to create database tables", error=str(e), exc_info=True)
        raise


async def get_async_session() -> AsyncGenerator[AsyncSession, None]:
    """Get async database session"""
    if not async_session_maker:
        raise RuntimeError("Database not initialized. Call init_db() first.")
    
    async with async_session_maker() as session:
        try:
            yield session
        except Exception as e:
            await session.rollback()
            logger.error("Database session error", error=str(e), exc_info=True)
            raise
        finally:
            await session.close()


def get_sync_session():
    """Get sync database session"""
    if not sync_session_maker:
        raise RuntimeError("Database not initialized. Call init_db() first.")
    
    with sync_session_maker() as session:
        try:
            yield session
        except Exception as e:
            session.rollback()
            logger.error("Sync database session error", error=str(e), exc_info=True)
            raise
        finally:
            session.close()


# Database health check
async def check_database_health() -> bool:
    """Check if database is healthy"""
    try:
        async with async_engine.begin() as conn:
            result = await conn.execute(text("SELECT 1 as health_check"))
            row = result.fetchone()
            return row[0] == 1
    except Exception as e:
        logger.error("Database health check failed", error=str(e))
        return False


# Database utilities
class DatabaseManager:
    """Database management utilities"""
    
    @staticmethod
    async def execute_raw_sql(query: str, params: dict = None) -> list:
        """Execute raw SQL query"""
        async with async_engine.begin() as conn:
            result = await conn.execute(text(query), params or {})
            return result.fetchall()
    
    @staticmethod
    async def get_table_info(table_name: str) -> dict:
        """Get table information"""
        query = """
        SELECT column_name, data_type, is_nullable, column_default
        FROM information_schema.columns 
        WHERE table_name = :table_name
        ORDER BY ordinal_position
        """
        
        rows = await DatabaseManager.execute_raw_sql(query, {"table_name": table_name})
        return [
            {
                "column_name": row[0],
                "data_type": row[1],
                "is_nullable": row[2],
                "column_default": row[3],
            }
            for row in rows
        ]
    
    @staticmethod
    async def get_database_stats() -> dict:
        """Get database statistics"""
        try:
            # Get table sizes
            size_query = """
            SELECT 
                schemaname,
                tablename,
                pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size
            FROM pg_tables 
            WHERE schemaname NOT IN ('information_schema', 'pg_catalog')
            ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC
            """
            
            # Get connection count
            conn_query = "SELECT count(*) FROM pg_stat_activity"
            
            table_sizes = await DatabaseManager.execute_raw_sql(size_query)
            connections = await DatabaseManager.execute_raw_sql(conn_query)
            
            return {
                "table_sizes": [
                    {"schema": row[0], "table": row[1], "size": row[2]}
                    for row in table_sizes
                ],
                "active_connections": connections[0][0] if connections else 0,
                "engine_pool_size": async_engine.pool.size(),
                "engine_checked_out": async_engine.pool.checkedout(),
            }
            
        except Exception as e:
            logger.error("Failed to get database stats", error=str(e))
            return {"error": str(e)}


# Transaction decorator
def transactional(func):
    """Decorator to wrap function in database transaction"""
    async def wrapper(*args, **kwargs):
        async with async_session_maker() as session:
            try:
                async with session.begin():
                    # Inject session as first argument if not present
                    if 'session' not in kwargs:
                        kwargs['session'] = session
                    result = await func(*args, **kwargs)
                    return result
            except Exception as e:
                await session.rollback()
                logger.error("Transaction failed", error=str(e), exc_info=True)
                raise
    
    return wrapper


# Connection pool monitoring
async def monitor_connection_pool():
    """Monitor database connection pool"""
    while True:
        try:
            if async_engine:
                pool = async_engine.pool
                logger.info(
                    "Database pool status",
                    size=pool.size(),
                    checked_out=pool.checkedout(),
                    overflow=pool.overflow(),
                    checked_in=pool.checkedin(),
                )
            
            await asyncio.sleep(60)  # Check every minute
            
        except Exception as e:
            logger.error("Pool monitoring error", error=str(e))
            await asyncio.sleep(60)
