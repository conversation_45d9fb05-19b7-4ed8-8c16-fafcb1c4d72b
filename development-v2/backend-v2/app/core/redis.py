"""
Redis configuration for caching and rate limiting
"""

import asyncio
import json
from typing import Any, Optional, Union

import redis.asyncio as redis
import structlog
from redis.exceptions import ConnectionError, TimeoutError

from app.core.config import get_settings

logger = structlog.get_logger(__name__)
settings = get_settings()

# Global Redis connection pool
redis_pool: Optional[redis.ConnectionPool] = None
redis_client: Optional[redis.Redis] = None


async def init_redis() -> None:
    """Initialize Redis connection pool"""
    global redis_pool, redis_client
    
    try:
        # Create connection pool
        redis_pool = redis.ConnectionPool.from_url(
            settings.REDIS_URL,
            max_connections=settings.REDIS_POOL_SIZE,
            retry_on_timeout=True,
            socket_keepalive=True,
            socket_keepalive_options={},
            health_check_interval=30,
        )
        
        # Create Redis client
        redis_client = redis.Redis(
            connection_pool=redis_pool,
            decode_responses=True,
        )
        
        # Test connection
        await redis_client.ping()
        
        logger.info("Redis connection established", redis_url=settings.REDIS_URL)
        
    except Exception as e:
        logger.error("Failed to initialize Redis", error=str(e), exc_info=True)
        raise


async def close_redis() -> None:
    """Close Redis connections"""
    global redis_pool, redis_client
    
    if redis_client:
        await redis_client.close()
        logger.info("Redis client closed")
    
    if redis_pool:
        await redis_pool.disconnect()
        logger.info("Redis pool disconnected")


async def get_redis() -> redis.Redis:
    """Get Redis client instance"""
    if not redis_client:
        raise RuntimeError("Redis not initialized. Call init_redis() first.")
    return redis_client


# Redis health check
async def check_redis_health() -> bool:
    """Check if Redis is healthy"""
    try:
        client = await get_redis()
        await client.ping()
        return True
    except Exception as e:
        logger.error("Redis health check failed", error=str(e))
        return False


# Cache utilities
class RedisCache:
    """Redis caching utilities"""
    
    def __init__(self, prefix: str = "chartgenius"):
        self.prefix = prefix
    
    def _make_key(self, key: str) -> str:
        """Create prefixed cache key"""
        return f"{self.prefix}:{key}"
    
    async def get(self, key: str) -> Optional[Any]:
        """Get value from cache"""
        try:
            client = await get_redis()
            value = await client.get(self._make_key(key))
            
            if value is None:
                return None
            
            # Try to deserialize JSON
            try:
                return json.loads(value)
            except json.JSONDecodeError:
                return value
                
        except Exception as e:
            logger.error("Cache get error", key=key, error=str(e))
            return None
    
    async def set(self, key: str, value: Any, ttl: int = 3600) -> bool:
        """Set value in cache with TTL"""
        try:
            client = await get_redis()
            
            # Serialize to JSON if not string
            if not isinstance(value, str):
                value = json.dumps(value, default=str)
            
            await client.setex(self._make_key(key), ttl, value)
            return True
            
        except Exception as e:
            logger.error("Cache set error", key=key, error=str(e))
            return False
    
    async def delete(self, key: str) -> bool:
        """Delete key from cache"""
        try:
            client = await get_redis()
            result = await client.delete(self._make_key(key))
            return result > 0
            
        except Exception as e:
            logger.error("Cache delete error", key=key, error=str(e))
            return False
    
    async def exists(self, key: str) -> bool:
        """Check if key exists in cache"""
        try:
            client = await get_redis()
            result = await client.exists(self._make_key(key))
            return result > 0
            
        except Exception as e:
            logger.error("Cache exists error", key=key, error=str(e))
            return False
    
    async def increment(self, key: str, amount: int = 1, ttl: int = None) -> int:
        """Increment counter in cache"""
        try:
            client = await get_redis()
            cache_key = self._make_key(key)
            
            # Use pipeline for atomic operations
            pipe = client.pipeline()
            pipe.incr(cache_key, amount)
            
            if ttl:
                pipe.expire(cache_key, ttl)
            
            results = await pipe.execute()
            return results[0]
            
        except Exception as e:
            logger.error("Cache increment error", key=key, error=str(e))
            return 0


# Rate limiting
class RedisRateLimiter:
    """Redis-based rate limiter using token bucket algorithm"""
    
    def __init__(self, prefix: str = "ratelimit"):
        self.prefix = prefix
    
    def _make_key(self, identifier: str, window: str) -> str:
        """Create rate limit key"""
        return f"{self.prefix}:{identifier}:{window}"
    
    async def is_allowed(
        self, 
        identifier: str, 
        limit: int, 
        window_seconds: int,
        cost: int = 1
    ) -> tuple[bool, dict]:
        """
        Check if request is allowed under rate limit
        Returns (allowed, info_dict)
        """
        try:
            client = await get_redis()
            key = self._make_key(identifier, f"{window_seconds}s")
            
            # Use Lua script for atomic rate limiting
            lua_script = """
            local key = KEYS[1]
            local limit = tonumber(ARGV[1])
            local window = tonumber(ARGV[2])
            local cost = tonumber(ARGV[3])
            local now = tonumber(ARGV[4])
            
            local current = redis.call('GET', key)
            if current == false then
                current = 0
            else
                current = tonumber(current)
            end
            
            if current + cost <= limit then
                local new_count = redis.call('INCRBY', key, cost)
                redis.call('EXPIRE', key, window)
                return {1, new_count, limit - new_count}
            else
                local ttl = redis.call('TTL', key)
                return {0, current, 0, ttl}
            end
            """
            
            import time
            result = await client.eval(
                lua_script,
                1,
                key,
                limit,
                window_seconds,
                cost,
                int(time.time())
            )
            
            allowed = bool(result[0])
            current_count = result[1]
            remaining = result[2] if allowed else 0
            reset_time = result[3] if not allowed else window_seconds
            
            return allowed, {
                "limit": limit,
                "remaining": remaining,
                "reset_time": reset_time,
                "current": current_count,
            }
            
        except Exception as e:
            logger.error("Rate limit check error", identifier=identifier, error=str(e))
            # Fail open - allow request if Redis is down
            return True, {"limit": limit, "remaining": limit - 1, "reset_time": window_seconds}
    
    async def reset(self, identifier: str, window_seconds: int) -> bool:
        """Reset rate limit for identifier"""
        try:
            client = await get_redis()
            key = self._make_key(identifier, f"{window_seconds}s")
            result = await client.delete(key)
            return result > 0
            
        except Exception as e:
            logger.error("Rate limit reset error", identifier=identifier, error=str(e))
            return False


# Session management
class RedisSessionManager:
    """Redis-based session management"""
    
    def __init__(self, prefix: str = "session"):
        self.prefix = prefix
    
    def _make_key(self, session_id: str) -> str:
        """Create session key"""
        return f"{self.prefix}:{session_id}"
    
    async def create_session(self, session_id: str, data: dict, ttl: int = 3600) -> bool:
        """Create new session"""
        try:
            client = await get_redis()
            session_data = json.dumps(data, default=str)
            await client.setex(self._make_key(session_id), ttl, session_data)
            return True
            
        except Exception as e:
            logger.error("Session create error", session_id=session_id, error=str(e))
            return False
    
    async def get_session(self, session_id: str) -> Optional[dict]:
        """Get session data"""
        try:
            client = await get_redis()
            data = await client.get(self._make_key(session_id))
            
            if data is None:
                return None
            
            return json.loads(data)
            
        except Exception as e:
            logger.error("Session get error", session_id=session_id, error=str(e))
            return None
    
    async def update_session(self, session_id: str, data: dict, ttl: int = 3600) -> bool:
        """Update session data"""
        return await self.create_session(session_id, data, ttl)
    
    async def delete_session(self, session_id: str) -> bool:
        """Delete session"""
        try:
            client = await get_redis()
            result = await client.delete(self._make_key(session_id))
            return result > 0
            
        except Exception as e:
            logger.error("Session delete error", session_id=session_id, error=str(e))
            return False
    
    async def extend_session(self, session_id: str, ttl: int = 3600) -> bool:
        """Extend session TTL"""
        try:
            client = await get_redis()
            result = await client.expire(self._make_key(session_id), ttl)
            return result
            
        except Exception as e:
            logger.error("Session extend error", session_id=session_id, error=str(e))
            return False


# Global instances
cache = RedisCache()
rate_limiter = RedisRateLimiter()
session_manager = RedisSessionManager()
