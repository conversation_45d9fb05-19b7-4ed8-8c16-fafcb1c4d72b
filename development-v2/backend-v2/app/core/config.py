"""
Configuration settings for ChartGenius Backend v2
Based on environment variables from ChartGenius_TZ/env_vars.md
"""

import os
from functools import lru_cache
from typing import List, Optional

from pydantic import Field, validator
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """Application settings"""
    
    # Application
    APP_NAME: str = "ChartGenius"
    VERSION: str = "2.0.0-dev"
    DEBUG: bool = Field(default=False, env="DEBUG")
    
    # Server
    HOST: str = Field(default="0.0.0.0", env="HOST")
    PORT: int = Field(default=8001, env="PORT")
    
    # Security
    SECRET_KEY: str = Field(..., env="SECRET_KEY")
    JWT_ALGORITHM: str = Field(default="RS256", env="JWT_ALGORITHM")
    JWT_PRIVATE_KEY: str = Field(..., env="JWT_PRIVATE_KEY")
    JWT_PUBLIC_KEY: str = Field(..., env="JWT_PUBLIC_KEY")
    JWT_EXPIRE_MINUTES: int = Field(default=60, env="JWT_EXPIRE_MINUTES")
    
    # CORS
    ALLOWED_ORIGINS: List[str] = Field(
        default=["http://localhost:3001", "https://chartgenius.dev"],
        env="ALLOWED_ORIGINS"
    )
    ALLOWED_HOSTS: List[str] = Field(
        default=["localhost", "chartgenius.dev", "*.chartgenius.dev"],
        env="ALLOWED_HOSTS"
    )
    
    # Database (Oracle Autonomous JSON DB)
    DATABASE_URL: str = Field(..., env="DATABASE_URL")
    DATABASE_POOL_SIZE: int = Field(default=10, env="DATABASE_POOL_SIZE")
    DATABASE_MAX_OVERFLOW: int = Field(default=20, env="DATABASE_MAX_OVERFLOW")
    
    # Redis
    REDIS_URL: str = Field(default="redis://localhost:6379/0", env="REDIS_URL")
    REDIS_POOL_SIZE: int = Field(default=10, env="REDIS_POOL_SIZE")
    
    # Telegram Bot
    TELEGRAM_BOT_TOKEN: str = Field(..., env="TELEGRAM_BOT_TOKEN")
    TELEGRAM_WEBHOOK_URL: Optional[str] = Field(default=None, env="TELEGRAM_WEBHOOK_URL")
    TELEGRAM_WEBHOOK_SECRET: Optional[str] = Field(default=None, env="TELEGRAM_WEBHOOK_SECRET")
    
    # LLM Providers
    OPENAI_API_KEY: str = Field(..., env="OPENAI_API_KEY")
    OPENAI_MODEL: str = Field(default="gpt-4", env="OPENAI_MODEL")
    OPENAI_MAX_TOKENS: int = Field(default=4000, env="OPENAI_MAX_TOKENS")
    
    # Gemini CLI
    GEMINI_CLI_CONFIG: str = Field(..., env="GEMINI_CLI_CONFIG")
    GEMINI_MODEL: str = Field(default="gemini-pro", env="GEMINI_MODEL")
    GEMINI_MAX_TOKENS: int = Field(default=4000, env="GEMINI_MAX_TOKENS")
    
    # Rate Limiting (Redis-based)
    GEMINI_RATE_LIMIT_RPM: int = Field(default=60, env="GEMINI_RATE_LIMIT_RPM")
    GEMINI_RATE_LIMIT_RPD: int = Field(default=1000, env="GEMINI_RATE_LIMIT_RPD")
    API_RATE_LIMIT_PER_MINUTE: int = Field(default=100, env="API_RATE_LIMIT_PER_MINUTE")
    
    # Stripe
    STRIPE_SECRET_KEY: str = Field(..., env="STRIPE_SECRET_KEY")
    STRIPE_PUBLISHABLE_KEY: str = Field(..., env="STRIPE_PUBLISHABLE_KEY")
    STRIPE_WEBHOOK_SECRET: str = Field(..., env="STRIPE_WEBHOOK_SECRET")
    
    # Oracle Cloud
    OCI_CONFIG_FILE: str = Field(default="~/.oci/config", env="OCI_CONFIG_FILE")
    OCI_PROFILE: str = Field(default="DEFAULT", env="OCI_PROFILE")
    OCI_COMPARTMENT_ID: str = Field(..., env="OCI_COMPARTMENT_ID")
    OCI_REGION: str = Field(default="eu-frankfurt-1", env="OCI_REGION")
    
    # Object Storage
    OCI_BUCKET_NAME: str = Field(..., env="OCI_BUCKET_NAME")
    OCI_NAMESPACE: str = Field(..., env="OCI_NAMESPACE")
    
    # Monitoring
    SENTRY_DSN: Optional[str] = Field(default=None, env="SENTRY_DSN")
    LOG_LEVEL: str = Field(default="INFO", env="LOG_LEVEL")
    
    # Background Tasks
    CELERY_BROKER_URL: str = Field(default="redis://localhost:6379/1", env="CELERY_BROKER_URL")
    CELERY_RESULT_BACKEND: str = Field(default="redis://localhost:6379/2", env="CELERY_RESULT_BACKEND")
    
    # External APIs
    CCXT_SANDBOX: bool = Field(default=True, env="CCXT_SANDBOX")
    
    @validator("ALLOWED_ORIGINS", pre=True)
    def parse_cors_origins(cls, v):
        if isinstance(v, str):
            return [origin.strip() for origin in v.split(",")]
        return v
    
    @validator("ALLOWED_HOSTS", pre=True)
    def parse_allowed_hosts(cls, v):
        if isinstance(v, str):
            return [host.strip() for host in v.split(",")]
        return v
    
    @validator("LOG_LEVEL")
    def validate_log_level(cls, v):
        valid_levels = ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]
        if v.upper() not in valid_levels:
            raise ValueError(f"LOG_LEVEL must be one of {valid_levels}")
        return v.upper()
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = True


@lru_cache()
def get_settings() -> Settings:
    """Get cached settings instance"""
    return Settings()


# Environment-specific configurations
class DevelopmentSettings(Settings):
    """Development environment settings"""
    DEBUG: bool = True
    LOG_LEVEL: str = "DEBUG"
    
    class Config:
        env_file = ".env.development"


class ProductionSettings(Settings):
    """Production environment settings"""
    DEBUG: bool = False
    LOG_LEVEL: str = "INFO"
    
    class Config:
        env_file = ".env.production"


class TestSettings(Settings):
    """Test environment settings"""
    DEBUG: bool = True
    LOG_LEVEL: str = "DEBUG"
    DATABASE_URL: str = "sqlite+aiosqlite:///./test.db"
    REDIS_URL: str = "redis://localhost:6379/15"  # Use different DB for tests
    
    class Config:
        env_file = ".env.test"


def get_settings_for_environment(env: str = None) -> Settings:
    """Get settings for specific environment"""
    env = env or os.getenv("ENVIRONMENT", "development").lower()
    
    if env == "production":
        return ProductionSettings()
    elif env == "test":
        return TestSettings()
    else:
        return DevelopmentSettings()
