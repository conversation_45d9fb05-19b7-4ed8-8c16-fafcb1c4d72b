"""
Custom exceptions and error handlers for ChartGenius Backend v2
"""

from typing import Any, Dict, Optional

import structlog
from fastapi import Fast<PERSON><PERSON>, HTTPException, Request, status
from fastapi.responses import JSONResponse
from pydantic import BaseModel, ValidationError

logger = structlog.get_logger(__name__)


# Custom exception classes
class ChartGeniusException(Exception):
    """Base exception for ChartGenius application"""
    
    def __init__(
        self,
        message: str,
        status_code: int = status.HTTP_500_INTERNAL_SERVER_ERROR,
        details: Optional[Dict[str, Any]] = None,
        error_code: Optional[str] = None,
    ):
        self.message = message
        self.status_code = status_code
        self.details = details or {}
        self.error_code = error_code or self.__class__.__name__
        super().__init__(self.message)


class AuthenticationError(ChartGeniusException):
    """Authentication related errors"""
    
    def __init__(self, message: str = "Authentication failed", details: Optional[Dict] = None):
        super().__init__(
            message=message,
            status_code=status.HTTP_401_UNAUTHORIZED,
            details=details,
            error_code="AUTHENTICATION_ERROR",
        )


class AuthorizationError(ChartGeniusException):
    """Authorization related errors"""
    
    def __init__(self, message: str = "Access denied", details: Optional[Dict] = None):
        super().__init__(
            message=message,
            status_code=status.HTTP_403_FORBIDDEN,
            details=details,
            error_code="AUTHORIZATION_ERROR",
        )


class ValidationError(ChartGeniusException):
    """Data validation errors"""
    
    def __init__(self, message: str = "Validation failed", details: Optional[Dict] = None):
        super().__init__(
            message=message,
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            details=details,
            error_code="VALIDATION_ERROR",
        )


class NotFoundError(ChartGeniusException):
    """Resource not found errors"""
    
    def __init__(self, message: str = "Resource not found", details: Optional[Dict] = None):
        super().__init__(
            message=message,
            status_code=status.HTTP_404_NOT_FOUND,
            details=details,
            error_code="NOT_FOUND_ERROR",
        )


class ConflictError(ChartGeniusException):
    """Resource conflict errors"""
    
    def __init__(self, message: str = "Resource conflict", details: Optional[Dict] = None):
        super().__init__(
            message=message,
            status_code=status.HTTP_409_CONFLICT,
            details=details,
            error_code="CONFLICT_ERROR",
        )


class RateLimitError(ChartGeniusException):
    """Rate limiting errors"""
    
    def __init__(
        self, 
        message: str = "Rate limit exceeded", 
        details: Optional[Dict] = None,
        retry_after: Optional[int] = None,
    ):
        details = details or {}
        if retry_after:
            details["retry_after"] = retry_after
        
        super().__init__(
            message=message,
            status_code=status.HTTP_429_TOO_MANY_REQUESTS,
            details=details,
            error_code="RATE_LIMIT_ERROR",
        )


class ExternalServiceError(ChartGeniusException):
    """External service errors (LLM, payment, etc.)"""
    
    def __init__(
        self, 
        message: str = "External service error", 
        details: Optional[Dict] = None,
        service_name: Optional[str] = None,
    ):
        details = details or {}
        if service_name:
            details["service"] = service_name
        
        super().__init__(
            message=message,
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            details=details,
            error_code="EXTERNAL_SERVICE_ERROR",
        )


class DatabaseError(ChartGeniusException):
    """Database related errors"""
    
    def __init__(self, message: str = "Database error", details: Optional[Dict] = None):
        super().__init__(
            message=message,
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            details=details,
            error_code="DATABASE_ERROR",
        )


class PaymentError(ChartGeniusException):
    """Payment processing errors"""
    
    def __init__(
        self, 
        message: str = "Payment processing failed", 
        details: Optional[Dict] = None,
        payment_provider: Optional[str] = None,
    ):
        details = details or {}
        if payment_provider:
            details["provider"] = payment_provider
        
        super().__init__(
            message=message,
            status_code=status.HTTP_402_PAYMENT_REQUIRED,
            details=details,
            error_code="PAYMENT_ERROR",
        )


# Error response models
class ErrorResponse(BaseModel):
    """Standard error response model"""
    error: str
    message: str
    status_code: int
    details: Optional[Dict[str, Any]] = None
    timestamp: str
    request_id: Optional[str] = None


class ValidationErrorResponse(BaseModel):
    """Validation error response model"""
    error: str = "VALIDATION_ERROR"
    message: str = "Validation failed"
    status_code: int = 422
    details: Dict[str, Any]
    timestamp: str
    request_id: Optional[str] = None


# Exception handlers
async def chartgenius_exception_handler(request: Request, exc: ChartGeniusException) -> JSONResponse:
    """Handle ChartGenius custom exceptions"""
    import time
    
    # Log the error
    logger.error(
        "ChartGenius exception occurred",
        error_code=exc.error_code,
        message=exc.message,
        status_code=exc.status_code,
        details=exc.details,
        path=request.url.path,
        method=request.method,
        exc_info=True,
    )
    
    # Create error response
    error_response = ErrorResponse(
        error=exc.error_code,
        message=exc.message,
        status_code=exc.status_code,
        details=exc.details,
        timestamp=str(int(time.time())),
        request_id=getattr(request.state, "request_id", None),
    )
    
    # Add rate limit headers for rate limit errors
    headers = {}
    if isinstance(exc, RateLimitError) and "retry_after" in exc.details:
        headers["Retry-After"] = str(exc.details["retry_after"])
    
    return JSONResponse(
        status_code=exc.status_code,
        content=error_response.model_dump(),
        headers=headers,
    )


async def http_exception_handler(request: Request, exc: HTTPException) -> JSONResponse:
    """Handle FastAPI HTTP exceptions"""
    import time
    
    logger.warning(
        "HTTP exception occurred",
        status_code=exc.status_code,
        detail=exc.detail,
        path=request.url.path,
        method=request.method,
    )
    
    error_response = ErrorResponse(
        error="HTTP_ERROR",
        message=exc.detail,
        status_code=exc.status_code,
        timestamp=str(int(time.time())),
        request_id=getattr(request.state, "request_id", None),
    )
    
    return JSONResponse(
        status_code=exc.status_code,
        content=error_response.model_dump(),
    )


async def validation_exception_handler(request: Request, exc: ValidationError) -> JSONResponse:
    """Handle Pydantic validation exceptions"""
    import time
    
    # Format validation errors
    errors = []
    for error in exc.errors():
        errors.append({
            "field": ".".join(str(x) for x in error["loc"]),
            "message": error["msg"],
            "type": error["type"],
        })
    
    logger.warning(
        "Validation error occurred",
        errors=errors,
        path=request.url.path,
        method=request.method,
    )
    
    error_response = ValidationErrorResponse(
        details={"validation_errors": errors},
        timestamp=str(int(time.time())),
        request_id=getattr(request.state, "request_id", None),
    )
    
    return JSONResponse(
        status_code=422,
        content=error_response.model_dump(),
    )


async def general_exception_handler(request: Request, exc: Exception) -> JSONResponse:
    """Handle unexpected exceptions"""
    import time
    
    logger.error(
        "Unexpected exception occurred",
        error=str(exc),
        error_type=type(exc).__name__,
        path=request.url.path,
        method=request.method,
        exc_info=True,
    )
    
    # Don't expose internal errors in production
    from app.core.config import get_settings
    settings = get_settings()
    
    if settings.DEBUG:
        message = f"Internal server error: {str(exc)}"
        details = {"error_type": type(exc).__name__}
    else:
        message = "Internal server error"
        details = None
    
    error_response = ErrorResponse(
        error="INTERNAL_SERVER_ERROR",
        message=message,
        status_code=500,
        details=details,
        timestamp=str(int(time.time())),
        request_id=getattr(request.state, "request_id", None),
    )
    
    return JSONResponse(
        status_code=500,
        content=error_response.model_dump(),
    )


def setup_exception_handlers(app: FastAPI) -> None:
    """Setup all exception handlers"""
    app.add_exception_handler(ChartGeniusException, chartgenius_exception_handler)
    app.add_exception_handler(HTTPException, http_exception_handler)
    app.add_exception_handler(ValidationError, validation_exception_handler)
    app.add_exception_handler(Exception, general_exception_handler)
