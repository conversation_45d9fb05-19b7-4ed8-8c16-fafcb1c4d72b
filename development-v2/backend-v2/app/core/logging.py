"""
Structured logging configuration for ChartGenius Backend v2
"""

import logging
import sys
from typing import Any, Dict

import structlog
from structlog.types import Processor


def setup_logging(log_level: str = "INFO") -> None:
    """Setup structured logging with structlog"""
    
    # Configure standard library logging
    logging.basicConfig(
        format="%(message)s",
        stream=sys.stdout,
        level=getattr(logging, log_level.upper()),
    )
    
    # Configure structlog
    structlog.configure(
        processors=[
            # Add log level to event dict
            structlog.stdlib.add_log_level,
            # Add logger name to event dict
            structlog.stdlib.add_logger_name,
            # Add timestamp
            structlog.processors.TimeStamper(fmt="iso"),
            # Add stack info for exceptions
            structlog.processors.StackInfoRenderer(),
            # Format exceptions
            structlog.dev.set_exc_info,
            # Add process and thread info
            add_process_info,
            # JSON formatter for production, console for development
            structlog.dev.Console<PERSON>enderer() if log_level == "DEBUG" 
            else structlog.processors.JSONRenderer(),
        ],
        wrapper_class=structlog.stdlib.BoundLogger,
        logger_factory=structlog.stdlib.LoggerFactory(),
        context_class=dict,
        cache_logger_on_first_use=True,
    )


def add_process_info(logger: Any, method_name: str, event_dict: Dict[str, Any]) -> Dict[str, Any]:
    """Add process and thread information to log events"""
    import os
    import threading
    
    event_dict["process_id"] = os.getpid()
    event_dict["thread_id"] = threading.get_ident()
    event_dict["thread_name"] = threading.current_thread().name
    
    return event_dict


def get_logger(name: str = None) -> structlog.BoundLogger:
    """Get a structured logger instance"""
    return structlog.get_logger(name)


# Request ID middleware for tracing
class RequestIDProcessor:
    """Add request ID to all log events within a request context"""
    
    def __init__(self):
        self._context_var = None
    
    def __call__(self, logger: Any, method_name: str, event_dict: Dict[str, Any]) -> Dict[str, Any]:
        if self._context_var:
            request_id = self._context_var.get(None)
            if request_id:
                event_dict["request_id"] = request_id
        
        return event_dict


# Security-aware logging
def sanitize_sensitive_data(event_dict: Dict[str, Any]) -> Dict[str, Any]:
    """Remove or mask sensitive data from log events"""
    sensitive_keys = {
        "password", "token", "secret", "key", "authorization", 
        "api_key", "private_key", "webhook_secret"
    }
    
    def _sanitize_value(key: str, value: Any) -> Any:
        if isinstance(key, str) and any(sensitive in key.lower() for sensitive in sensitive_keys):
            if isinstance(value, str) and len(value) > 8:
                return f"{value[:4]}***{value[-4:]}"
            return "***"
        
        if isinstance(value, dict):
            return {k: _sanitize_value(k, v) for k, v in value.items()}
        elif isinstance(value, list):
            return [_sanitize_value("", item) for item in value]
        
        return value
    
    return {k: _sanitize_value(k, v) for k, v in event_dict.items()}


# Performance logging
class PerformanceLogger:
    """Log performance metrics"""
    
    def __init__(self, logger: structlog.BoundLogger):
        self.logger = logger
    
    def log_api_call(self, endpoint: str, method: str, duration_ms: float, status_code: int):
        """Log API call performance"""
        self.logger.info(
            "API call completed",
            endpoint=endpoint,
            method=method,
            duration_ms=duration_ms,
            status_code=status_code,
            performance=True,
        )
    
    def log_database_query(self, query_type: str, duration_ms: float, rows_affected: int = None):
        """Log database query performance"""
        self.logger.info(
            "Database query completed",
            query_type=query_type,
            duration_ms=duration_ms,
            rows_affected=rows_affected,
            performance=True,
        )
    
    def log_llm_request(self, provider: str, model: str, tokens: int, duration_ms: float):
        """Log LLM request performance"""
        self.logger.info(
            "LLM request completed",
            provider=provider,
            model=model,
            tokens=tokens,
            duration_ms=duration_ms,
            performance=True,
        )


# Error logging with context
class ErrorLogger:
    """Enhanced error logging with context"""
    
    def __init__(self, logger: structlog.BoundLogger):
        self.logger = logger
    
    def log_api_error(self, error: Exception, endpoint: str, user_id: str = None, request_data: Dict = None):
        """Log API errors with context"""
        self.logger.error(
            "API error occurred",
            error=str(error),
            error_type=type(error).__name__,
            endpoint=endpoint,
            user_id=user_id,
            request_data=sanitize_sensitive_data(request_data or {}),
            exc_info=True,
        )
    
    def log_llm_error(self, error: Exception, provider: str, model: str, prompt_length: int = None):
        """Log LLM errors with context"""
        self.logger.error(
            "LLM error occurred",
            error=str(error),
            error_type=type(error).__name__,
            provider=provider,
            model=model,
            prompt_length=prompt_length,
            exc_info=True,
        )
    
    def log_database_error(self, error: Exception, operation: str, table: str = None):
        """Log database errors with context"""
        self.logger.error(
            "Database error occurred",
            error=str(error),
            error_type=type(error).__name__,
            operation=operation,
            table=table,
            exc_info=True,
        )


# Audit logging
class AuditLogger:
    """Audit logging for security and compliance"""
    
    def __init__(self, logger: structlog.BoundLogger):
        self.logger = logger
    
    def log_user_action(self, user_id: str, action: str, resource: str = None, details: Dict = None):
        """Log user actions for audit trail"""
        self.logger.info(
            "User action",
            user_id=user_id,
            action=action,
            resource=resource,
            details=sanitize_sensitive_data(details or {}),
            audit=True,
        )
    
    def log_admin_action(self, admin_id: str, action: str, target_user: str = None, details: Dict = None):
        """Log admin actions for audit trail"""
        self.logger.info(
            "Admin action",
            admin_id=admin_id,
            action=action,
            target_user=target_user,
            details=sanitize_sensitive_data(details or {}),
            audit=True,
            security=True,
        )
    
    def log_authentication(self, user_id: str, success: bool, method: str, ip_address: str = None):
        """Log authentication attempts"""
        self.logger.info(
            "Authentication attempt",
            user_id=user_id,
            success=success,
            method=method,
            ip_address=ip_address,
            audit=True,
            security=True,
        )
    
    def log_payment(self, user_id: str, amount: float, currency: str, provider: str, success: bool):
        """Log payment transactions"""
        self.logger.info(
            "Payment transaction",
            user_id=user_id,
            amount=amount,
            currency=currency,
            provider=provider,
            success=success,
            audit=True,
            financial=True,
        )
