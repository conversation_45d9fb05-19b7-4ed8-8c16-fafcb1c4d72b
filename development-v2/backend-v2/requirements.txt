# ChartGenius Backend v2 Dependencies
# FastAPI 0.115 and core dependencies
fastapi==0.115.14
uvicorn[standard]==0.30.0
pydantic==2.8.0
pydantic-settings==2.4.0

# Database and ORM
sqlmodel==0.0.21
asyncpg==0.29.0
aiosqlite==0.20.0
alembic==1.13.2

# Authentication and Security
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
python-multipart==0.0.9

# HTTP Client
httpx==0.27.0
aiohttp==3.9.5

# Redis for caching and rate limiting
redis==5.0.7

# Background tasks
celery==5.3.4
flower==2.0.1

# Monitoring and logging
structlog==24.2.0
sentry-sdk[fastapi]==2.8.0
prometheus-client==0.20.0

# Data validation and serialization
orjson==3.10.6
python-dateutil==2.9.0

# Telegram Bot
aiogram==3.20.0
aiofiles==24.1.0

# LLM Integration
openai==1.35.15
google-generativeai==0.7.2

# Payment processing
stripe==10.5.0

# Image processing
Pillow==10.4.0
matplotlib==3.9.1
plotly==5.22.0

# Crypto data
ccxt==4.3.74
websockets==12.0

# Development dependencies
pytest==8.2.2
pytest-asyncio==0.23.7
pytest-cov==5.0.0
black==24.4.2
isort==5.13.2
flake8==7.1.0
mypy==1.10.1

# Oracle Cloud SDK
oci==2.126.4

# Environment management
python-dotenv==1.0.1
