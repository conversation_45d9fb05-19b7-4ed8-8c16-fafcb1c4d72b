# Development environment variables for ChartGenius Backend v2
DEBUG=true
ENVIRONMENT=development
HOST=0.0.0.0
PORT=8001

# Security
SECRET_KEY=dev-secret-key-change-in-production
JWT_ALGORITHM=RS256
JWT_PRIVATE_KEY=-----BEGIN RSA PRIVATE KEY-----
# Add your RSA private key here for development
-----END RSA PRIVATE KEY-----
JWT_PUBLIC_KEY=-----BEGIN PUBLIC KEY-----
# Add your RSA public key here for development
-----END PUBLIC KEY-----
JWT_EXPIRE_MINUTES=60

# CORS and Hosts
ALLOWED_ORIGINS=http://localhost:3001,http://localhost:3000,https://localhost:3001
ALLOWED_HOSTS=localhost,127.0.0.1,0.0.0.0

# Database (PostgreSQL for development)
DATABASE_URL=postgresql+asyncpg://chartgenius:chartgenius_dev_pass@postgres:5432/chartgenius_dev
DATABASE_POOL_SIZE=10
DATABASE_MAX_OVERFLOW=20

# Redis
REDIS_URL=redis://redis:6379/0
REDIS_POOL_SIZE=10

# Telegram Bot
TELEGRAM_BOT_TOKEN=**********:AAERodVAje0VnifJmUJWeq0EM4FxMueXrB0
TELEGRAM_WEBHOOK_URL=
TELEGRAM_WEBHOOK_SECRET=

# LLM Providers
OPENAI_API_KEY=your-openai-api-key-here
OPENAI_MODEL=gpt-4
OPENAI_MAX_TOKENS=4000

# Gemini CLI
GEMINI_CLI_CONFIG=/path/to/gemini/config
GEMINI_MODEL=gemini-pro
GEMINI_MAX_TOKENS=4000

# Rate Limiting
GEMINI_RATE_LIMIT_RPM=60
GEMINI_RATE_LIMIT_RPD=1000
API_RATE_LIMIT_PER_MINUTE=100

# Stripe
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key
STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret

# Oracle Cloud
OCI_CONFIG_FILE=~/.oci/config
OCI_PROFILE=DEFAULT
OCI_COMPARTMENT_ID=your-compartment-id
OCI_REGION=eu-frankfurt-1
OCI_BUCKET_NAME=chartgenius-dev
OCI_NAMESPACE=your-namespace

# Monitoring
SENTRY_DSN=
LOG_LEVEL=DEBUG

# Background Tasks
CELERY_BROKER_URL=redis://redis:6379/1
CELERY_RESULT_BACKEND=redis://redis:6379/2

# External APIs
CCXT_SANDBOX=true
