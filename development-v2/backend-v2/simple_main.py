#!/usr/bin/env python3
"""
ChartGenius v2 Simple Backend for Testing
Minimal FastAPI server for quick testing without complex dependencies
"""

import os
import json
from datetime import datetime
from typing import Dict, List, Any

from fastapi import Fast<PERSON><PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import uvicorn

# Simple FastAPI app
app = FastAPI(
    title="ChartGenius v2 Backend (Simple)",
    description="Simplified backend for testing",
    version="2.0.0-dev",
    docs_url="/docs",
    redoc_url="/redoc"
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # In production, specify exact origins
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Health check endpoint
@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "version": "2.0.0-dev",
        "timestamp": datetime.utcnow().isoformat(),
        "environment": os.getenv("ENVIRONMENT", "development"),
        "debug": os.getenv("DEBUG", "false").lower() == "true"
    }

# Root endpoint
@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "message": "ChartGenius v2 Backend API",
        "version": "2.0.0-dev",
        "docs": "/docs",
        "health": "/health"
    }

# API v2 routes
@app.get("/api/v2/")
async def api_root():
    """API v2 root"""
    return {
        "message": "ChartGenius v2 API",
        "version": "2.0.0",
        "endpoints": {
            "signals": "/api/v2/signals/",
            "analysis": "/api/v2/analysis/",
            "users": "/api/v2/users/",
            "auth": "/api/v2/auth/"
        }
    }

# Mock signals endpoint
@app.get("/api/v2/signals/")
async def get_signals():
    """Get trading signals (mock data)"""
    mock_signals = [
        {
            "id": 1,
            "symbol": "BTCUSDT",
            "timeframe": "1h",
            "signal_type": "LONG",
            "confidence": 0.85,
            "entry_price": 43250.0,
            "stop_loss": 42800.0,
            "take_profit": 44500.0,
            "created_at": "2025-01-01T18:00:00Z",
            "status": "active"
        },
        {
            "id": 2,
            "symbol": "ETHUSDT",
            "timeframe": "4h",
            "signal_type": "SHORT",
            "confidence": 0.78,
            "entry_price": 3420.0,
            "stop_loss": 3480.0,
            "take_profit": 3280.0,
            "created_at": "2025-01-01T16:00:00Z",
            "status": "active"
        }
    ]
    return {
        "signals": mock_signals,
        "total": len(mock_signals),
        "timestamp": datetime.utcnow().isoformat()
    }

# Mock analysis endpoint
@app.get("/api/v2/analysis/{symbol}")
async def get_analysis(symbol: str):
    """Get analysis for a symbol (mock data)"""
    if symbol.upper() not in ["BTCUSDT", "ETHUSDT", "ADAUSDT"]:
        raise HTTPException(status_code=404, detail="Symbol not found")
    
    mock_analysis = {
        "symbol": symbol.upper(),
        "timeframe": "1h",
        "analysis": {
            "trend": "bullish" if symbol.upper() == "BTCUSDT" else "bearish",
            "support_levels": [42800, 42200, 41500],
            "resistance_levels": [44500, 45200, 46000],
            "rsi": 65.4,
            "macd": {
                "signal": "buy" if symbol.upper() == "BTCUSDT" else "sell",
                "histogram": 0.25
            },
            "volume_analysis": "increasing",
            "recommendation": "LONG" if symbol.upper() == "BTCUSDT" else "SHORT"
        },
        "generated_at": datetime.utcnow().isoformat(),
        "confidence": 0.82
    }
    
    return mock_analysis

# Mock user info endpoint
@app.get("/api/v2/users/me")
async def get_current_user():
    """Get current user info (mock data)"""
    return {
        "id": 1,
        "telegram_id": 299820674,
        "username": "test_user",
        "first_name": "Test",
        "last_name": "User",
        "tier": "premium",
        "subscription": {
            "active": True,
            "expires_at": "2025-12-31T23:59:59Z",
            "features": ["signals", "analysis", "alerts"]
        },
        "created_at": "2024-01-01T00:00:00Z"
    }

# Mock auth endpoint
@app.post("/api/v2/auth/telegram")
async def telegram_auth(auth_data: Dict[str, Any]):
    """Telegram authentication (mock)"""
    return {
        "access_token": "mock_jwt_token_here",
        "token_type": "bearer",
        "expires_in": 3600,
        "user": {
            "id": 1,
            "telegram_id": auth_data.get("id", 299820674),
            "username": auth_data.get("username", "test_user"),
            "first_name": auth_data.get("first_name", "Test"),
            "tier": "premium"
        }
    }

# Mock LLM status endpoint
@app.get("/api/v2/llm/status")
async def llm_status():
    """LLM providers status"""
    return {
        "providers": {
            "openai": {
                "status": "available",
                "model": "gpt-4",
                "rate_limit": "60 RPM"
            },
            "gemini": {
                "status": "available", 
                "model": "gemini-2.5-pro",
                "rate_limit": "60 RPM"
            }
        },
        "active_provider": "openai",
        "fallback_enabled": True
    }

# Mock admin stats endpoint
@app.get("/admin/stats")
async def admin_stats():
    """Admin statistics"""
    return {
        "users": {
            "total": 1250,
            "active_today": 89,
            "premium": 156,
            "vip": 23
        },
        "signals": {
            "total_today": 12,
            "accuracy": 0.78,
            "active": 5
        },
        "system": {
            "uptime": "2 days, 14 hours",
            "version": "2.0.0-dev",
            "environment": "development"
        }
    }

if __name__ == "__main__":
    # Get configuration from environment
    host = os.getenv("HOST", "0.0.0.0")
    port = int(os.getenv("PORT", "8001"))
    debug = os.getenv("DEBUG", "true").lower() == "true"
    
    print(f"🚀 Starting ChartGenius v2 Simple Backend")
    print(f"📍 Host: {host}:{port}")
    print(f"🔧 Debug: {debug}")
    print(f"📚 Docs: http://{host}:{port}/docs")
    
    # Run the server
    uvicorn.run(
        "simple_main:app",
        host=host,
        port=port,
        reload=debug,
        log_level="debug" if debug else "info"
    )
