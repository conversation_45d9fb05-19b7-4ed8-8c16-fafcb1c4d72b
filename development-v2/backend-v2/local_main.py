#!/usr/bin/env python3
"""
ChartGenius v2 Local Backend
Simplified FastAPI server for local testing
"""

import os
import json
import redis
from datetime import datetime
from typing import Dict, List, Any, Optional

from fastapi import Fast<PERSON>I, HTTPException, Depends
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import uvicorn

# Initialize FastAPI app
app = FastAPI(
    title="ChartGenius v2 Backend (Local)",
    description="Local backend for testing ChartGenius v2",
    version="2.0.0-dev",
    docs_url="/docs",
    redoc_url="/redoc"
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3001", "http://127.0.0.1:3001"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Redis connection
redis_client = None

def get_redis():
    """Get Redis client"""
    global redis_client
    if redis_client is None:
        try:
            redis_client = redis.Redis(host='localhost', port=6379, db=0, decode_responses=True)
            redis_client.ping()
            print("✅ Redis connected successfully")
        except Exception as e:
            print(f"❌ Redis connection failed: {e}")
            redis_client = None
    return redis_client

# Startup event
@app.on_event("startup")
async def startup_event():
    """Initialize services on startup"""
    print("🚀 Starting ChartGenius v2 Local Backend...")
    
    # Test Redis connection
    redis_conn = get_redis()
    if redis_conn:
        redis_conn.set("startup_time", datetime.utcnow().isoformat())
        print("✅ Redis connection established")
    else:
        print("⚠️ Redis not available - running without cache")
    
    print("✅ Backend startup complete!")

# Health check endpoint
@app.get("/health")
async def health_check():
    """Health check endpoint"""
    redis_status = "connected" if get_redis() else "disconnected"
    
    return {
        "status": "healthy",
        "version": "2.0.0-dev",
        "timestamp": datetime.utcnow().isoformat(),
        "environment": "local",
        "services": {
            "redis": redis_status,
            "database": "mock",
            "llm": "mock"
        }
    }

# Root endpoint
@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "message": "ChartGenius v2 Local Backend API",
        "version": "2.0.0-dev",
        "docs": "/docs",
        "health": "/health",
        "endpoints": {
            "api": "/api/v2/",
            "signals": "/api/v2/signals/",
            "analysis": "/api/v2/analysis/",
            "users": "/api/v2/users/",
            "auth": "/api/v2/auth/"
        }
    }

# API v2 routes
@app.get("/api/v2/")
async def api_root():
    """API v2 root"""
    return {
        "message": "ChartGenius v2 API",
        "version": "2.0.0",
        "status": "local_testing",
        "features": ["signals", "analysis", "auth", "monitoring"]
    }

# Mock signals endpoint
@app.get("/api/v2/signals/")
async def get_signals():
    """Get trading signals (mock data)"""
    
    # Try to get from Redis cache
    redis_conn = get_redis()
    if redis_conn:
        cached_signals = redis_conn.get("signals:latest")
        if cached_signals:
            return json.loads(cached_signals)
    
    # Generate mock signals
    mock_signals = [
        {
            "id": 1,
            "symbol": "BTCUSDT",
            "timeframe": "1h",
            "signal_type": "LONG",
            "confidence": 0.85,
            "entry_price": 43250.0,
            "stop_loss": 42800.0,
            "take_profit": 44500.0,
            "created_at": datetime.utcnow().isoformat(),
            "status": "active",
            "analysis": {
                "rsi": 65.4,
                "macd": "bullish",
                "volume": "increasing"
            }
        },
        {
            "id": 2,
            "symbol": "ETHUSDT",
            "timeframe": "4h",
            "signal_type": "SHORT",
            "confidence": 0.78,
            "entry_price": 3420.0,
            "stop_loss": 3480.0,
            "take_profit": 3280.0,
            "created_at": datetime.utcnow().isoformat(),
            "status": "active",
            "analysis": {
                "rsi": 35.2,
                "macd": "bearish",
                "volume": "decreasing"
            }
        },
        {
            "id": 3,
            "symbol": "ADAUSDT",
            "timeframe": "1d",
            "signal_type": "LONG",
            "confidence": 0.72,
            "entry_price": 0.45,
            "stop_loss": 0.42,
            "take_profit": 0.52,
            "created_at": datetime.utcnow().isoformat(),
            "status": "pending",
            "analysis": {
                "rsi": 58.7,
                "macd": "neutral",
                "volume": "stable"
            }
        }
    ]
    
    result = {
        "signals": mock_signals,
        "total": len(mock_signals),
        "timestamp": datetime.utcnow().isoformat(),
        "source": "local_mock"
    }
    
    # Cache in Redis if available
    if redis_conn:
        redis_conn.setex("signals:latest", 60, json.dumps(result))  # Cache for 1 minute
    
    return result

# Mock analysis endpoint
@app.get("/api/v2/analysis/{symbol}")
async def get_analysis(symbol: str):
    """Get analysis for a symbol (mock data)"""
    symbol = symbol.upper()
    
    if symbol not in ["BTCUSDT", "ETHUSDT", "ADAUSDT", "BNBUSDT", "SOLUSDT"]:
        raise HTTPException(status_code=404, detail=f"Analysis for {symbol} not found")
    
    # Mock analysis data
    mock_analysis = {
        "symbol": symbol,
        "timeframe": "1h",
        "analysis": {
            "trend": "bullish" if symbol in ["BTCUSDT", "ETHUSDT"] else "bearish",
            "support_levels": [42800, 42200, 41500] if symbol == "BTCUSDT" else [3200, 3100, 3000],
            "resistance_levels": [44500, 45200, 46000] if symbol == "BTCUSDT" else [3500, 3600, 3700],
            "technical_indicators": {
                "rsi": 65.4 if symbol == "BTCUSDT" else 45.2,
                "macd": {
                    "signal": "buy" if symbol == "BTCUSDT" else "sell",
                    "histogram": 0.25 if symbol == "BTCUSDT" else -0.15
                },
                "bollinger_bands": {
                    "upper": 44000,
                    "middle": 43000,
                    "lower": 42000
                },
                "volume_analysis": "increasing" if symbol == "BTCUSDT" else "decreasing"
            },
            "recommendation": "LONG" if symbol in ["BTCUSDT", "ETHUSDT"] else "SHORT",
            "risk_level": "medium"
        },
        "generated_at": datetime.utcnow().isoformat(),
        "confidence": 0.82,
        "source": "local_mock_ai"
    }
    
    return mock_analysis

# Mock user info endpoint
@app.get("/api/v2/users/me")
async def get_current_user():
    """Get current user info (mock data)"""
    return {
        "id": 1,
        "telegram_id": 299820674,
        "username": "test_user",
        "first_name": "Test",
        "last_name": "User",
        "tier": "premium",
        "subscription": {
            "active": True,
            "expires_at": "2025-12-31T23:59:59Z",
            "features": ["signals", "analysis", "alerts", "premium_support"]
        },
        "stats": {
            "signals_received": 156,
            "successful_trades": 89,
            "win_rate": 0.57
        },
        "created_at": "2024-01-01T00:00:00Z",
        "last_active": datetime.utcnow().isoformat()
    }

# Mock auth endpoint
@app.post("/api/v2/auth/telegram")
async def telegram_auth(auth_data: Dict[str, Any]):
    """Telegram authentication (mock)"""
    return {
        "access_token": "mock_jwt_token_here_" + str(datetime.utcnow().timestamp()),
        "token_type": "bearer",
        "expires_in": 3600,
        "user": {
            "id": 1,
            "telegram_id": auth_data.get("id", 299820674),
            "username": auth_data.get("username", "test_user"),
            "first_name": auth_data.get("first_name", "Test"),
            "tier": "premium"
        }
    }

# Mock auth profile endpoint
@app.get("/api/v2/auth/profile")
async def get_auth_profile():
    """Get authenticated user profile (mock)"""
    return {
        "id": 1,
        "telegram_id": 299820674,
        "username": "test_user",
        "first_name": "Test",
        "last_name": "User",
        "role": "admin",  # Make user admin for testing
        "tier": "premium",
        "subscription": {
            "active": True,
            "expires_at": "2025-12-31T23:59:59Z",
            "features": ["signals", "analysis", "alerts", "premium_support", "admin_panel"]
        },
        "stats": {
            "signals_received": 156,
            "successful_trades": 89,
            "win_rate": 0.57
        },
        "created_at": "2024-01-01T00:00:00Z",
        "last_active": datetime.utcnow().isoformat()
    }

# Mock LLM status endpoint
@app.get("/api/v2/llm/status")
async def llm_status():
    """LLM providers status"""
    return {
        "providers": {
            "openai": {
                "status": "available",
                "model": "gpt-4",
                "rate_limit": "60 RPM",
                "last_request": datetime.utcnow().isoformat()
            },
            "gemini": {
                "status": "available", 
                "model": "gemini-2.5-pro",
                "rate_limit": "60 RPM",
                "last_request": datetime.utcnow().isoformat()
            }
        },
        "active_provider": "openai",
        "fallback_enabled": True,
        "total_requests_today": 42
    }

# Mock admin stats endpoint
@app.get("/admin/stats")
async def admin_stats():
    """Admin statistics"""
    return {
        "users": {
            "total": 1250,
            "active_today": 89,
            "premium": 156,
            "vip": 23,
            "new_today": 12
        },
        "signals": {
            "total_today": 12,
            "accuracy": 0.78,
            "active": 5,
            "successful": 9
        },
        "system": {
            "uptime": "2 days, 14 hours",
            "version": "2.0.0-dev",
            "environment": "local",
            "redis_status": "connected" if get_redis() else "disconnected"
        },
        "performance": {
            "avg_response_time": "45ms",
            "requests_per_minute": 23,
            "error_rate": 0.02
        }
    }

# WebSocket endpoint for real-time updates (mock)
@app.get("/ws/signals")
async def websocket_info():
    """WebSocket connection info"""
    return {
        "message": "WebSocket endpoint for real-time signals",
        "url": "ws://localhost:8001/ws/signals",
        "status": "available",
        "features": ["real_time_signals", "price_updates", "alerts"]
    }

if __name__ == "__main__":
    print("🚀 Starting ChartGenius v2 Local Backend")
    print("📍 URL: http://localhost:8001")
    print("📚 Docs: http://localhost:8001/docs")
    print("🔍 Health: http://localhost:8001/health")
    print("📊 API: http://localhost:8001/api/v2/")
    
    # Run the server
    uvicorn.run(
        "local_main:app",
        host="0.0.0.0",
        port=8001,
        reload=True,
        log_level="info"
    )
