-- ChartGenius v2 Seed Data
-- Sample data for development environment

SET search_path TO chartgenius, public;

-- Insert admin user (from ТЗ)
INSERT INTO users (tg_id, email, role, tier, first_name, username, language_code, settings, is_active) 
VALUES (
    '299820674',
    '<EMAIL>',
    'admin',
    'vip',
    'Admin',
    'chartgenius_admin',
    'ru',
    '{"language": "ru", "timezone": "Europe/Moscow", "notifications": {"signals": true, "news": true, "system": true}, "trading": {"default_symbol": "BTCUSDT", "default_timeframe": "4h", "risk_level": "medium"}, "ui": {"theme": "dark", "compact_mode": false, "show_tooltips": true}}',
    true
) ON CONFLICT (tg_id) DO NOTHING;

-- Insert demo users for testing
INSERT INTO users (tg_id, email, role, tier, first_name, last_name, username, language_code, total_analyses, total_signals_received) VALUES
('123456789', '<EMAIL>', 'premium', 'premium', 'Demo', 'User', 'demo_user_1', 'en', 15, 45),
('987654321', '<EMAIL>', 'vip', 'vip', 'VIP', 'Trader', 'vip_trader', 'ru', 89, 234),
('555666777', '<EMAIL>', 'user', 'free', 'Free', 'User', 'free_user', 'ru', 3, 8),
('111222333', '<EMAIL>', 'premium', 'premium', 'Premium', 'Analyst', 'premium_analyst', 'en', 34, 67)
ON CONFLICT (tg_id) DO NOTHING;

-- Insert sample subscriptions
INSERT INTO subscriptions (user_id, tier, price, exp_ts, source, status, currency, auto_renew) VALUES
('123456789', 'premium', 29.99, EXTRACT(EPOCH FROM NOW() + INTERVAL '30 days')::INTEGER, 'stripe', 'active', 'USD', true),
('987654321', 'vip', 79.99, EXTRACT(EPOCH FROM NOW() + INTERVAL '30 days')::INTEGER, 'stripe', 'active', 'USD', true),
('111222333', 'premium', 29.99, EXTRACT(EPOCH FROM NOW() + INTERVAL '15 days')::INTEGER, 'telegram', 'active', 'USD', false)
ON CONFLICT DO NOTHING;

-- Insert sample signals
INSERT INTO signals (symbol, tf, text_md, ts, signal_type, confidence, entry_price, stop_loss, take_profit, risk_level, llm_provider, llm_model, status) VALUES
(
    'BTCUSDT',
    '4h',
    '# Bitcoin Technical Analysis

## Market Overview
Bitcoin is showing strong bullish momentum with a clear breakout above the $67,000 resistance level. The price action suggests continuation of the uptrend.

## Key Levels
- **Support**: $65,800
- **Resistance**: $69,500
- **Entry**: $67,420

## Technical Indicators
- RSI: 68 (bullish but not overbought)
- MACD: Bullish crossover confirmed
- Volume: Above average, confirming the move

## Trading Recommendation
**LONG** position with tight risk management. Target the next resistance at $69,500.',
    EXTRACT(EPOCH FROM NOW() - INTERVAL '2 hours')::INTEGER,
    'long',
    85.5,
    67420.50,
    65800.00,
    69500.00,
    'medium',
    'gemini',
    'gemini-pro',
    'active'
),
(
    'ETHUSDT',
    '1h',
    '# Ethereum Short-term Analysis

## Market Sentiment
Ethereum is facing resistance at the $3,250 level with decreasing volume. Potential pullback expected.

## Key Observations
- Bearish divergence on RSI
- Volume declining on recent highs
- Support at $3,180

## Recommendation
**SHORT** position targeting $3,180 support level.',
    EXTRACT(EPOCH FROM NOW() - INTERVAL '1 hour')::INTEGER,
    'short',
    78.2,
    3245.80,
    3280.00,
    3180.00,
    'medium',
    'openai',
    'gpt-4',
    'active'
),
(
    'ADAUSDT',
    '1d',
    '# Cardano Long-term Outlook

## Fundamental Analysis
Strong development activity and upcoming network upgrades support bullish outlook.

## Technical Setup
- Breaking above key resistance
- High volume confirmation
- Bullish flag pattern completion

## Target
Long-term target at $0.52 with stop at $0.42.',
    EXTRACT(EPOCH FROM NOW() - INTERVAL '4 hours')::INTEGER,
    'long',
    92.1,
    0.4521,
    0.4200,
    0.5200,
    'low',
    'gemini',
    'gemini-pro',
    'triggered'
)
ON CONFLICT DO NOTHING;

-- Insert sample analyses
INSERT INTO analyses (user_id, symbol, timeframe, analysis_type, title, content, summary, llm_provider, llm_model, tokens_used, status) VALUES
(
    '299820674',
    'BTCUSDT',
    '4h',
    'technical',
    'Bitcoin 4H Technical Analysis',
    'Comprehensive technical analysis of Bitcoin showing bullish momentum with key resistance breakout...',
    'Bitcoin shows strong bullish signals with target at $69,500',
    'gemini',
    'gemini-pro',
    2450,
    'completed'
),
(
    '123456789',
    'ETHUSDT',
    '1h',
    'technical',
    'Ethereum Short-term Analysis',
    'Ethereum facing resistance with potential pullback scenario...',
    'Short-term bearish outlook for Ethereum',
    'openai',
    'gpt-4',
    1890,
    'completed'
)
ON CONFLICT DO NOTHING;

-- Insert sample watchlists
INSERT INTO watchlists (user_id, name, description, symbols, is_default) VALUES
('299820674', 'Main Portfolio', 'Primary trading symbols', '["BTCUSDT", "ETHUSDT", "ADAUSDT", "BNBUSDT"]', true),
('123456789', 'Altcoins', 'Alternative cryptocurrencies', '["ADAUSDT", "DOTUSDT", "LINKUSDT", "UNIUSDT"]', true),
('987654321', 'DeFi Tokens', 'Decentralized Finance tokens', '["UNIUSDT", "AAVEUSDT", "COMPUSDT", "SUSHIUSDT"]', false)
ON CONFLICT (user_id, name) DO NOTHING;

-- Update statistics
UPDATE users SET 
    total_analyses = (SELECT COUNT(*) FROM analyses WHERE analyses.user_id = users.tg_id),
    total_signals_received = (SELECT COUNT(*) FROM signals WHERE signals.created_at > users.created_at)
WHERE tg_id IN ('299820674', '123456789', '987654321', '555666777', '111222333');
