#!/bin/bash

# ChartGenius v2 Health Check Script
# Проверка состояния всех компонентов системы

set -e

echo "🔍 ChartGenius v2 Health Check"
echo "============================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Counters
TOTAL_CHECKS=0
PASSED_CHECKS=0
FAILED_CHECKS=0

# Function to perform health check
check_service() {
    local service_name="$1"
    local check_command="$2"
    local expected_result="$3"
    
    TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
    
    echo -n "  🔍 $service_name... "
    
    if eval "$check_command" > /dev/null 2>&1; then
        echo -e "${GREEN}✅ OK${NC}"
        PASSED_CHECKS=$((PASSED_CHECKS + 1))
        return 0
    else
        echo -e "${RED}❌ FAILED${NC}"
        FAILED_CHECKS=$((FAILED_CHECKS + 1))
        return 1
    fi
}

# Function to check HTTP endpoint
check_http() {
    local name="$1"
    local url="$2"
    local expected_status="${3:-200}"
    
    TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
    
    echo -n "  🌐 $name... "
    
    status_code=$(curl -s -o /dev/null -w "%{http_code}" "$url" 2>/dev/null || echo "000")
    
    if [ "$status_code" = "$expected_status" ]; then
        echo -e "${GREEN}✅ OK ($status_code)${NC}"
        PASSED_CHECKS=$((PASSED_CHECKS + 1))
        return 0
    else
        echo -e "${RED}❌ FAILED ($status_code)${NC}"
        FAILED_CHECKS=$((FAILED_CHECKS + 1))
        return 1
    fi
}

# Function to check JSON response
check_json_response() {
    local name="$1"
    local url="$2"
    local json_path="$3"
    local expected_value="$4"
    
    TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
    
    echo -n "  📊 $name... "
    
    response=$(curl -s "$url" 2>/dev/null || echo "{}")
    actual_value=$(echo "$response" | jq -r "$json_path" 2>/dev/null || echo "null")
    
    if [ "$actual_value" = "$expected_value" ]; then
        echo -e "${GREEN}✅ OK${NC}"
        PASSED_CHECKS=$((PASSED_CHECKS + 1))
        return 0
    else
        echo -e "${RED}❌ FAILED (got: $actual_value, expected: $expected_value)${NC}"
        FAILED_CHECKS=$((FAILED_CHECKS + 1))
        return 1
    fi
}

echo -e "${BLUE}🐳 Docker Services${NC}"
check_service "Docker daemon" "docker info"
check_service "Docker Compose" "docker-compose version"

echo -e "\n${BLUE}📦 Container Status${NC}"
services=("postgres" "redis" "backend-v2" "frontend-v2" "bot-v2" "prometheus" "grafana" "nginx")

for service in "${services[@]}"; do
    check_service "$service container" "docker-compose ps $service | grep -q 'Up'"
done

echo -e "\n${BLUE}🗄️  Database Connectivity${NC}"
check_service "PostgreSQL connection" "docker-compose exec -T postgres pg_isready -U chartgenius -d chartgenius_dev"
check_service "Redis connection" "docker-compose exec -T redis redis-cli ping | grep -q PONG"

echo -e "\n${BLUE}🌐 HTTP Endpoints${NC}"
check_http "Frontend" "http://localhost:3001" "200"
check_http "Backend API" "http://localhost:8001" "200"
check_http "Backend Health" "http://localhost:8001/health" "200"
check_http "Backend Docs" "http://localhost:8001/docs" "200"
check_http "Bot Webhook" "http://localhost:8080/health" "200"
check_http "Prometheus" "http://localhost:9090" "200"
check_http "Grafana" "http://localhost:3000/login" "200"
check_http "Flower" "http://localhost:5555" "401"  # Expects auth

echo -e "\n${BLUE}📊 API Functionality${NC}"
check_json_response "Backend Health Status" "http://localhost:8001/health" ".status" "healthy"
check_json_response "Backend Version" "http://localhost:8001/health" ".version" "2.0.0-dev"

# Check if backend can connect to database
check_http "Backend Database Check" "http://localhost:8001/api/v2/signals/" "200"

echo -e "\n${BLUE}🤖 Bot Functionality${NC}"
check_json_response "Bot Health Status" "http://localhost:8080/health" ".status" "healthy"

echo -e "\n${BLUE}📈 Monitoring${NC}"
check_http "Prometheus Targets" "http://localhost:9090/api/v1/targets" "200"
check_http "Grafana API" "http://localhost:3000/api/health" "200"

echo -e "\n${BLUE}🔧 Configuration${NC}"

# Check environment files
config_files=(
    "backend-v2/.env.development"
    "bot-v2/.env.development"
    "frontend-v2/.env.development"
)

for config_file in "${config_files[@]}"; do
    check_service "$config_file exists" "test -f $config_file"
done

# Check critical environment variables
echo -n "  🔑 Backend environment variables... "
if docker-compose exec -T backend-v2 python -c "
import os
required_vars = ['TELEGRAM_BOT_TOKEN', 'DATABASE_URL', 'REDIS_URL']
missing = [var for var in required_vars if not os.getenv(var)]
if missing:
    print(f'Missing: {missing}')
    exit(1)
" 2>/dev/null; then
    echo -e "${GREEN}✅ OK${NC}"
    PASSED_CHECKS=$((PASSED_CHECKS + 1))
else
    echo -e "${RED}❌ FAILED${NC}"
    FAILED_CHECKS=$((FAILED_CHECKS + 1))
fi
TOTAL_CHECKS=$((TOTAL_CHECKS + 1))

echo -e "\n${BLUE}💾 Data Persistence${NC}"

# Check if data directories exist and are writable
data_dirs=("data/postgres" "data/redis" "data/grafana" "data/prometheus")

for data_dir in "${data_dirs[@]}"; do
    if [ -d "$data_dir" ]; then
        check_service "$data_dir writable" "test -w $data_dir"
    else
        echo -e "  📁 $data_dir... ${YELLOW}⚠️  MISSING${NC}"
    fi
done

echo -e "\n${BLUE}🔒 Security${NC}"

# Check for default passwords in production
echo -n "  🔐 Default passwords check... "
if grep -r "password\|secret" .env* 2>/dev/null | grep -q "default\|example\|changeme"; then
    echo -e "${YELLOW}⚠️  DEFAULT PASSWORDS FOUND${NC}"
else
    echo -e "${GREEN}✅ OK${NC}"
    PASSED_CHECKS=$((PASSED_CHECKS + 1))
fi
TOTAL_CHECKS=$((TOTAL_CHECKS + 1))

# Check file permissions
check_service "Script permissions" "test -x scripts/start-dev.sh"

echo -e "\n${BLUE}📊 Performance${NC}"

# Check resource usage
echo -n "  💾 Memory usage... "
memory_usage=$(docker stats --no-stream --format "table {{.MemPerc}}" | tail -n +2 | sed 's/%//' | awk '{sum+=$1} END {print sum}')
if (( $(echo "$memory_usage < 80" | bc -l) )); then
    echo -e "${GREEN}✅ OK (${memory_usage}%)${NC}"
    PASSED_CHECKS=$((PASSED_CHECKS + 1))
else
    echo -e "${YELLOW}⚠️  HIGH (${memory_usage}%)${NC}"
fi
TOTAL_CHECKS=$((TOTAL_CHECKS + 1))

echo -e "\n${BLUE}🔄 Integration Tests${NC}"

# Test API integration
echo -n "  🔗 Frontend-Backend integration... "
if curl -s "http://localhost:3001" | grep -q "ChartGenius" 2>/dev/null; then
    echo -e "${GREEN}✅ OK${NC}"
    PASSED_CHECKS=$((PASSED_CHECKS + 1))
else
    echo -e "${RED}❌ FAILED${NC}"
    FAILED_CHECKS=$((FAILED_CHECKS + 1))
fi
TOTAL_CHECKS=$((TOTAL_CHECKS + 1))

# Test Bot-Backend integration
echo -n "  🤖 Bot-Backend integration... "
if docker-compose exec -T bot-v2 python -c "
import asyncio
import sys
sys.path.append('/app')
from app.services.backend_client import BackendClient

async def test():
    client = BackendClient('http://backend-v2:8001/api/v2')
    try:
        health = await client.get_health()
        await client.close()
        return health.get('status') == 'healthy'
    except:
        return False

result = asyncio.run(test())
exit(0 if result else 1)
" 2>/dev/null; then
    echo -e "${GREEN}✅ OK${NC}"
    PASSED_CHECKS=$((PASSED_CHECKS + 1))
else
    echo -e "${RED}❌ FAILED${NC}"
    FAILED_CHECKS=$((FAILED_CHECKS + 1))
fi
TOTAL_CHECKS=$((TOTAL_CHECKS + 1))

# Summary
echo ""
echo "================================="
echo -e "${BLUE}📋 Health Check Summary${NC}"
echo "================================="
echo ""
echo -e "Total checks: $TOTAL_CHECKS"
echo -e "${GREEN}Passed: $PASSED_CHECKS${NC}"
echo -e "${RED}Failed: $FAILED_CHECKS${NC}"
echo ""

if [ $FAILED_CHECKS -eq 0 ]; then
    echo -e "${GREEN}🎉 All checks passed! System is healthy.${NC}"
    exit 0
elif [ $FAILED_CHECKS -lt 3 ]; then
    echo -e "${YELLOW}⚠️  Some checks failed, but system is mostly functional.${NC}"
    exit 1
else
    echo -e "${RED}❌ Multiple checks failed. System needs attention.${NC}"
    echo ""
    echo -e "${BLUE}💡 Troubleshooting tips:${NC}"
    echo "1. Check service logs: docker-compose logs [service]"
    echo "2. Restart failed services: docker-compose restart [service]"
    echo "3. Check environment variables"
    echo "4. Verify network connectivity"
    echo "5. Check disk space and memory"
    exit 2
fi
