-- ChartGenius v2 Database Initialization Script
-- PostgreSQL schema for development environment

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";
CREATE EXTENSION IF NOT EXISTS "btree_gin";

-- <PERSON><PERSON> schemas
CREATE SCHEMA IF NOT EXISTS chartgenius;
CREATE SCHEMA IF NOT EXISTS analytics;
CREATE SCHEMA IF NOT EXISTS monitoring;

-- Set default schema
SET search_path TO chartgenius, public;

-- Users table (based on er.ddl.sql)
CREATE TABLE IF NOT EXISTS users (
    id SERIAL PRIMARY KEY,
    tg_id VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(255) UNIQUE,
    role VARCHAR(20) DEFAULT 'user' CHECK (role IN ('user', 'premium', 'vip', 'admin')),
    tier VARCHAR(20) DEFAULT 'free' CHECK (tier IN ('free', 'premium', 'vip')),
    exp_ts INTEGER,
    
    -- Profile information
    first_name VARCHA<PERSON>(100),
    last_name VA<PERSON>HA<PERSON>(100),
    username VARCHAR(100),
    language_code VARCHAR(10) DEFAULT 'ru',
    
    -- Settings (JSON)
    settings JSONB DEFAULT '{}',
    profile_data JSONB DEFAULT '{}',
    preferences JSONB DEFAULT '{}',
    
    -- Status
    is_active BOOLEAN DEFAULT TRUE,
    is_banned BOOLEAN DEFAULT FALSE,
    ban_reason TEXT,
    
    -- Statistics
    total_analyses INTEGER DEFAULT 0,
    total_signals_received INTEGER DEFAULT 0,
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_login_at TIMESTAMP WITH TIME ZONE
);

-- Subscriptions table
CREATE TABLE IF NOT EXISTS subscriptions (
    id SERIAL PRIMARY KEY,
    user_id VARCHAR(50) NOT NULL REFERENCES users(tg_id) ON DELETE CASCADE,
    tier VARCHAR(20) NOT NULL CHECK (tier IN ('free', 'premium', 'vip')),
    price DECIMAL(10,2) NOT NULL DEFAULT 0,
    exp_ts INTEGER NOT NULL,
    source VARCHAR(20) NOT NULL CHECK (source IN ('stripe', 'telegram', 'admin', 'promo')),
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'expired', 'cancelled', 'pending')),
    
    -- Payment details
    payment_id VARCHAR(255),
    currency VARCHAR(3) DEFAULT 'USD',
    payment_data JSONB DEFAULT '{}',
    
    -- Auto-renewal
    auto_renew BOOLEAN DEFAULT FALSE,
    renewal_price DECIMAL(10,2),
    
    -- Metadata
    metadata JSONB DEFAULT '{}',
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    activated_at TIMESTAMP WITH TIME ZONE,
    cancelled_at TIMESTAMP WITH TIME ZONE
);

-- Signals table
CREATE TABLE IF NOT EXISTS signals (
    id SERIAL PRIMARY KEY,
    symbol VARCHAR(20) NOT NULL,
    tf VARCHAR(10) NOT NULL CHECK (tf IN ('1m', '5m', '15m', '30m', '1h', '4h', '12h', '1d', '1w')),
    text_md TEXT NOT NULL,
    ts INTEGER NOT NULL,
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'triggered', 'expired', 'cancelled')),
    
    -- Signal details
    signal_type VARCHAR(10) CHECK (signal_type IN ('long', 'short', 'hold', 'neutral')),
    confidence DECIMAL(5,2) CHECK (confidence >= 0 AND confidence <= 100),
    
    -- Price levels
    entry_price DECIMAL(20,8),
    stop_loss DECIMAL(20,8),
    take_profit DECIMAL(20,8),
    
    -- Risk management
    risk_level VARCHAR(10) DEFAULT 'medium' CHECK (risk_level IN ('low', 'medium', 'high')),
    position_size DECIMAL(5,2) CHECK (position_size >= 0 AND position_size <= 100),
    
    -- Analysis metadata
    analysis_id INTEGER,
    llm_provider VARCHAR(20),
    llm_model VARCHAR(50),
    
    -- Technical analysis data (JSON)
    indicators JSONB DEFAULT '{}',
    market_data JSONB DEFAULT '{}',
    
    -- Performance tracking
    triggered_at TIMESTAMP WITH TIME ZONE,
    triggered_price DECIMAL(20,8),
    max_profit DECIMAL(10,4),
    max_loss DECIMAL(10,4),
    final_pnl DECIMAL(10,4),
    
    -- User interaction
    views INTEGER DEFAULT 0,
    likes INTEGER DEFAULT 0,
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    expires_at TIMESTAMP WITH TIME ZONE
);

-- Analyses table
CREATE TABLE IF NOT EXISTS analyses (
    id SERIAL PRIMARY KEY,
    user_id VARCHAR(50) NOT NULL REFERENCES users(tg_id) ON DELETE CASCADE,
    symbol VARCHAR(20) NOT NULL,
    timeframe VARCHAR(10) NOT NULL,
    analysis_type VARCHAR(20) DEFAULT 'technical',
    
    -- Content
    title VARCHAR(255),
    content TEXT NOT NULL,
    summary TEXT,
    
    -- LLM metadata
    llm_provider VARCHAR(20),
    llm_model VARCHAR(50),
    tokens_used INTEGER DEFAULT 0,
    
    -- Analysis data (JSON)
    market_data JSONB DEFAULT '{}',
    indicators JSONB DEFAULT '{}',
    recommendations JSONB DEFAULT '{}',
    
    -- Status
    status VARCHAR(20) DEFAULT 'completed' CHECK (status IN ('pending', 'processing', 'completed', 'failed')),
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Watchlists table
CREATE TABLE IF NOT EXISTS watchlists (
    id SERIAL PRIMARY KEY,
    user_id VARCHAR(50) NOT NULL REFERENCES users(tg_id) ON DELETE CASCADE,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    symbols JSONB DEFAULT '[]',
    is_default BOOLEAN DEFAULT FALSE,
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    UNIQUE(user_id, name)
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_users_tg_id ON users(tg_id);
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_users_role ON users(role);
CREATE INDEX IF NOT EXISTS idx_users_tier ON users(tier);
CREATE INDEX IF NOT EXISTS idx_users_created_at ON users(created_at);

CREATE INDEX IF NOT EXISTS idx_subscriptions_user_id ON subscriptions(user_id);
CREATE INDEX IF NOT EXISTS idx_subscriptions_status ON subscriptions(status);
CREATE INDEX IF NOT EXISTS idx_subscriptions_exp_ts ON subscriptions(exp_ts);

CREATE INDEX IF NOT EXISTS idx_signals_symbol ON signals(symbol);
CREATE INDEX IF NOT EXISTS idx_signals_tf ON signals(tf);
CREATE INDEX IF NOT EXISTS idx_signals_status ON signals(status);
CREATE INDEX IF NOT EXISTS idx_signals_created_at ON signals(created_at);
CREATE INDEX IF NOT EXISTS idx_signals_symbol_tf ON signals(symbol, tf);

CREATE INDEX IF NOT EXISTS idx_analyses_user_id ON analyses(user_id);
CREATE INDEX IF NOT EXISTS idx_analyses_symbol ON analyses(symbol);
CREATE INDEX IF NOT EXISTS idx_analyses_status ON analyses(status);
CREATE INDEX IF NOT EXISTS idx_analyses_created_at ON analyses(created_at);

CREATE INDEX IF NOT EXISTS idx_watchlists_user_id ON watchlists(user_id);

-- Create updated_at trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for updated_at
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_subscriptions_updated_at BEFORE UPDATE ON subscriptions
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_signals_updated_at BEFORE UPDATE ON signals
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_analyses_updated_at BEFORE UPDATE ON analyses
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_watchlists_updated_at BEFORE UPDATE ON watchlists
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Grant permissions
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA chartgenius TO chartgenius;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA chartgenius TO chartgenius;
