#!/bin/bash

# ChartGenius v2 Permissions Setup Script
# Настройка прав доступа для всех компонентов

set -e

echo "🔐 Setting up ChartGenius v2 Permissions"
echo "========================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Check if running as root (not recommended)
if [ "$EUID" -eq 0 ]; then
    echo -e "${YELLOW}⚠️  Running as root. Consider using a non-root user for development.${NC}"
fi

# Create necessary directories
echo -e "${BLUE}📁 Creating directories...${NC}"
directories=(
    "logs"
    "data"
    "data/postgres"
    "data/redis"
    "data/grafana"
    "data/prometheus"
    "nginx/logs"
    "monitoring/grafana/dashboards"
    "monitoring/prometheus/data"
)

for dir in "${directories[@]}"; do
    if [ ! -d "$dir" ]; then
        mkdir -p "$dir"
        echo "  ✅ Created: $dir"
    else
        echo "  ℹ️  Exists: $dir"
    fi
done

# Set directory permissions
echo -e "\n${BLUE}🔧 Setting directory permissions...${NC}"

# Data directories (need to be writable by containers)
chmod -R 755 data/ 2>/dev/null || true
chmod -R 755 logs/ 2>/dev/null || true

# Grafana data directory (specific UID/GID requirements)
if [ -d "data/grafana" ]; then
    chmod 755 data/grafana
    # Grafana runs as UID 472 in container
    if command -v chown &> /dev/null; then
        chown -R 472:472 data/grafana 2>/dev/null || true
    fi
fi

# Prometheus data directory
if [ -d "data/prometheus" ]; then
    chmod 755 data/prometheus
    # Prometheus runs as UID 65534 (nobody) in container
    if command -v chown &> /dev/null; then
        chown -R 65534:65534 data/prometheus 2>/dev/null || true
    fi
fi

echo "  ✅ Directory permissions set"

# Set script permissions
echo -e "\n${BLUE}📜 Setting script permissions...${NC}"
scripts=(
    "scripts/start-dev.sh"
    "scripts/stop-dev.sh"
    "scripts/health-check.sh"
    "scripts/migrate-from-v1.sh"
    "scripts/setup-permissions.sh"
)

for script in "${scripts[@]}"; do
    if [ -f "$script" ]; then
        chmod +x "$script"
        echo "  ✅ Executable: $script"
    else
        echo -e "  ${YELLOW}⚠️  Missing: $script${NC}"
    fi
done

# Set configuration file permissions
echo -e "\n${BLUE}⚙️  Setting configuration permissions...${NC}"

# Environment files (should be readable but not world-readable)
env_files=(
    "backend-v2/.env.development"
    "bot-v2/.env.development"
    "frontend-v2/.env.development"
)

for env_file in "${env_files[@]}"; do
    if [ -f "$env_file" ]; then
        chmod 640 "$env_file"
        echo "  ✅ Protected: $env_file"
    else
        echo -e "  ${YELLOW}⚠️  Missing: $env_file${NC}"
    fi
done

# SSL certificates (if they exist)
if [ -d "nginx/ssl" ]; then
    chmod 600 nginx/ssl/*.key 2>/dev/null || true
    chmod 644 nginx/ssl/*.crt 2>/dev/null || true
    chmod 644 nginx/ssl/*.pem 2>/dev/null || true
    echo "  ✅ SSL certificates protected"
fi

# Configuration files
config_files=(
    "nginx/nginx.conf"
    "config/redis.conf"
    "monitoring/prometheus/prometheus.yml"
)

for config_file in "${config_files[@]}"; do
    if [ -f "$config_file" ]; then
        chmod 644 "$config_file"
        echo "  ✅ Readable: $config_file"
    fi
done

# Docker files
docker_files=(
    "docker-compose.yml"
    "backend-v2/Dockerfile"
    "bot-v2/Dockerfile"
    "frontend-v2/Dockerfile.dev"
)

for docker_file in "${docker_files[@]}"; do
    if [ -f "$docker_file" ]; then
        chmod 644 "$docker_file"
        echo "  ✅ Readable: $docker_file"
    fi
done

# Set ownership for development (if not root)
if [ "$EUID" -ne 0 ] && command -v chown &> /dev/null; then
    echo -e "\n${BLUE}👤 Setting ownership...${NC}"
    
    current_user=$(whoami)
    current_group=$(id -gn)
    
    # Own the project directory
    chown -R "$current_user:$current_group" . 2>/dev/null || true
    echo "  ✅ Ownership set to $current_user:$current_group"
fi

# Create .gitignore for sensitive files
echo -e "\n${BLUE}🙈 Creating .gitignore entries...${NC}"

gitignore_entries=(
    "# ChartGenius v2 - Generated files"
    "logs/"
    "data/"
    "*.log"
    ".env.local"
    ".env.production"
    "migration-backup-*/"
    "nginx/logs/"
    "monitoring/prometheus/data/"
    "monitoring/grafana/data/"
    ""
    "# SSL certificates"
    "nginx/ssl/*.key"
    "nginx/ssl/*.crt"
    "nginx/ssl/*.pem"
    ""
    "# Temporary files"
    "*.tmp"
    "*.bak"
    ".DS_Store"
    "Thumbs.db"
)

# Add to .gitignore if it doesn't exist or entries are missing
if [ ! -f ".gitignore" ]; then
    printf '%s\n' "${gitignore_entries[@]}" > .gitignore
    echo "  ✅ Created .gitignore"
else
    # Check if our entries exist
    if ! grep -q "ChartGenius v2 - Generated files" .gitignore; then
        printf '\n%s\n' "${gitignore_entries[@]}" >> .gitignore
        echo "  ✅ Updated .gitignore"
    else
        echo "  ℹ️  .gitignore already configured"
    fi
fi

# Security check
echo -e "\n${BLUE}🔒 Security check...${NC}"

# Check for world-writable files
world_writable=$(find . -type f -perm -002 2>/dev/null | head -5)
if [ -n "$world_writable" ]; then
    echo -e "  ${YELLOW}⚠️  World-writable files found:${NC}"
    echo "$world_writable"
    echo "  Consider fixing permissions for security"
else
    echo "  ✅ No world-writable files found"
fi

# Check for files with passwords in names
password_files=$(find . -type f -name "*password*" -o -name "*secret*" -o -name "*key*" 2>/dev/null | grep -v ".git" | head -5)
if [ -n "$password_files" ]; then
    echo -e "  ${YELLOW}⚠️  Files with sensitive names found:${NC}"
    echo "$password_files"
    echo "  Ensure these files are properly protected"
fi

# Verify Docker permissions
echo -e "\n${BLUE}🐳 Docker permissions check...${NC}"

if groups | grep -q docker; then
    echo "  ✅ User is in docker group"
elif [ "$EUID" -eq 0 ]; then
    echo "  ✅ Running as root (has Docker access)"
else
    echo -e "  ${YELLOW}⚠️  User not in docker group. You may need to use sudo for Docker commands.${NC}"
    echo "  To fix: sudo usermod -aG docker $USER && newgrp docker"
fi

# Final summary
echo ""
echo "================================="
echo -e "${GREEN}🎉 Permissions Setup Complete!${NC}"
echo "================================="
echo ""
echo -e "${BLUE}📋 Summary:${NC}"
echo "  ✅ Directories created and configured"
echo "  ✅ Scripts made executable"
echo "  ✅ Configuration files protected"
echo "  ✅ Environment files secured"
echo "  ✅ .gitignore configured"
echo ""
echo -e "${BLUE}🚀 Next steps:${NC}"
echo "  1. Review environment files:"
echo "     - backend-v2/.env.development"
echo "     - bot-v2/.env.development"
echo "     - frontend-v2/.env.development"
echo ""
echo "  2. Start the development environment:"
echo "     ./scripts/start-dev.sh"
echo ""
echo "  3. Run health check:"
echo "     ./scripts/health-check.sh"
echo ""
echo -e "${YELLOW}💡 Security reminders:${NC}"
echo "  - Never commit .env files with real secrets"
echo "  - Use strong passwords in production"
echo "  - Regularly update dependencies"
echo "  - Monitor access logs"
