#!/bin/bash

# ChartGenius v2 Development Stop Script
# Остановка инфраструктуры разработки

set -e

echo "🛑 Stopping ChartGenius v2 Development Environment"
echo "================================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Parse command line arguments
REMOVE_VOLUMES=false
REMOVE_IMAGES=false

while [[ $# -gt 0 ]]; do
    case $1 in
        --volumes|-v)
            REMOVE_VOLUMES=true
            shift
            ;;
        --images|-i)
            REMOVE_IMAGES=true
            shift
            ;;
        --all|-a)
            REMOVE_VOLUMES=true
            REMOVE_IMAGES=true
            shift
            ;;
        --help|-h)
            echo "Usage: $0 [OPTIONS]"
            echo ""
            echo "Options:"
            echo "  --volumes, -v    Remove volumes (data will be lost)"
            echo "  --images, -i     Remove built images"
            echo "  --all, -a        Remove volumes and images"
            echo "  --help, -h       Show this help"
            exit 0
            ;;
        *)
            echo -e "${RED}Unknown option: $1${NC}"
            exit 1
            ;;
    esac
done

# Stop all services
echo -e "${BLUE}🛑 Stopping all services...${NC}"
docker-compose down

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ All services stopped${NC}"
else
    echo -e "${RED}❌ Error stopping services${NC}"
    exit 1
fi

# Remove volumes if requested
if [ "$REMOVE_VOLUMES" = true ]; then
    echo -e "${YELLOW}⚠️  Removing volumes (data will be lost)...${NC}"
    read -p "Are you sure? This will delete all data (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        docker-compose down -v
        echo -e "${GREEN}✅ Volumes removed${NC}"
    else
        echo -e "${BLUE}ℹ️  Volumes preserved${NC}"
    fi
fi

# Remove images if requested
if [ "$REMOVE_IMAGES" = true ]; then
    echo -e "${YELLOW}⚠️  Removing built images...${NC}"
    
    # Get image names
    images=$(docker-compose config | grep 'image:' | awk '{print $2}' | grep -E 'chartgenius|development-v2' || true)
    
    if [ -n "$images" ]; then
        echo "$images" | xargs docker rmi -f 2>/dev/null || true
        echo -e "${GREEN}✅ Images removed${NC}"
    else
        echo -e "${BLUE}ℹ️  No images to remove${NC}"
    fi
fi

# Clean up orphaned containers
echo -e "${BLUE}🧹 Cleaning up orphaned containers...${NC}"
docker container prune -f > /dev/null 2>&1 || true

# Clean up unused networks
echo -e "${BLUE}🧹 Cleaning up unused networks...${NC}"
docker network prune -f > /dev/null 2>&1 || true

# Show remaining containers
running_containers=$(docker ps --filter "name=development-v2" --format "table {{.Names}}\t{{.Status}}" 2>/dev/null || true)

if [ -n "$running_containers" ] && [ "$running_containers" != "NAMES	STATUS" ]; then
    echo -e "\n${YELLOW}⚠️  Some containers are still running:${NC}"
    echo "$running_containers"
else
    echo -e "\n${GREEN}✅ All ChartGenius containers stopped${NC}"
fi

# Show disk space freed (if volumes were removed)
if [ "$REMOVE_VOLUMES" = true ]; then
    echo -e "\n${BLUE}💾 Docker system cleanup...${NC}"
    docker system prune -f > /dev/null 2>&1 || true
fi

echo ""
echo -e "${GREEN}🎉 ChartGenius v2 Development Environment Stopped!${NC}"
echo "================================================="
echo ""
echo -e "${BLUE}📋 Next steps:${NC}"
echo "  Start again:  ./scripts/start-dev.sh"
echo "  View logs:    docker-compose logs [service]"
echo "  Full cleanup: $0 --all"
echo ""

if [ "$REMOVE_VOLUMES" = false ]; then
    echo -e "${BLUE}💡 Tip: Your data is preserved. Use --volumes to remove it.${NC}"
fi
