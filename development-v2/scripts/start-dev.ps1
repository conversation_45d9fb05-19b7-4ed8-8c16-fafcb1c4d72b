# ChartGenius v2 Development Startup Script for Windows
# PowerShell script to start the full development infrastructure

param(
    [switch]$SkipHealthCheck,
    [switch]$Verbose
)

# Set error action preference
$ErrorActionPreference = "Stop"

# Colors for output
$Colors = @{
    Red = "Red"
    Green = "Green"
    Yellow = "Yellow"
    Blue = "Blue"
    Cyan = "Cyan"
}

function Write-ColorOutput {
    param($Message, $Color = "White")
    Write-Host $Message -ForegroundColor $Color
}

function Test-Command {
    param($Command)
    try {
        Get-Command $Command -ErrorAction Stop | Out-Null
        return $true
    } catch {
        return $false
    }
}

function Wait-ForService {
    param($ServiceName, $TestCommand, $MaxWaitSeconds = 60)
    
    Write-ColorOutput "⏳ Waiting for $ServiceName to be ready..." $Colors.Blue
    
    $counter = 0
    do {
        Start-Sleep -Seconds 2
        $counter += 2
        
        try {
            $result = Invoke-Expression $TestCommand 2>$null
            if ($LASTEXITCODE -eq 0) {
                Write-ColorOutput "✅ $ServiceName is ready" $Colors.Green
                return $true
            }
        } catch {
            # Continue waiting
        }
        
        if ($counter -ge $MaxWaitSeconds) {
            Write-ColorOutput "❌ $ServiceName failed to start within $MaxWaitSeconds seconds" $Colors.Red
            return $false
        }
        
        Write-Host "." -NoNewline
    } while ($true)
}

# Main script
Write-ColorOutput "🚀 Starting ChartGenius v2 Development Environment" $Colors.Blue
Write-ColorOutput "==================================================" $Colors.Blue

# Check prerequisites
Write-ColorOutput "`n🔍 Checking prerequisites..." $Colors.Blue

if (!(Test-Command "docker")) {
    Write-ColorOutput "❌ Docker is not installed or not in PATH" $Colors.Red
    Write-ColorOutput "Please install Docker Desktop from https://www.docker.com/products/docker-desktop" $Colors.Yellow
    exit 1
}

if (!(Test-Command "docker-compose")) {
    Write-ColorOutput "❌ Docker Compose is not available" $Colors.Red
    exit 1
}

Write-ColorOutput "✅ Docker is available" $Colors.Green

# Check if Docker is running
try {
    docker info | Out-Null
    Write-ColorOutput "✅ Docker daemon is running" $Colors.Green
} catch {
    Write-ColorOutput "❌ Docker daemon is not running. Please start Docker Desktop." $Colors.Red
    exit 1
}

# Create necessary directories
Write-ColorOutput "`n📁 Creating directories..." $Colors.Blue

$directories = @(
    "data",
    "data\postgres",
    "data\redis", 
    "data\grafana",
    "data\prometheus",
    "logs",
    "nginx\logs"
)

foreach ($dir in $directories) {
    if (!(Test-Path $dir)) {
        New-Item -ItemType Directory -Force -Path $dir | Out-Null
        Write-ColorOutput "  ✅ Created: $dir" $Colors.Green
    } else {
        if ($Verbose) {
            Write-ColorOutput "  ℹ️  Exists: $dir" $Colors.Cyan
        }
    }
}

# Check environment files
Write-ColorOutput "`n📄 Checking environment files..." $Colors.Blue

$envFiles = @(
    @{Path = "backend-v2\.env.development"; Required = $true},
    @{Path = "bot-v2\.env.development"; Required = $true},
    @{Path = "frontend-v2\.env.development"; Required = $false}
)

foreach ($envFile in $envFiles) {
    if (!(Test-Path $envFile.Path)) {
        if ($envFile.Required) {
            Write-ColorOutput "⚠️  Missing required file: $($envFile.Path)" $Colors.Yellow
            Write-ColorOutput "Please create this file with appropriate configuration" $Colors.Yellow
        } else {
            Write-ColorOutput "ℹ️  Optional file missing: $($envFile.Path)" $Colors.Cyan
        }
    } else {
        Write-ColorOutput "  ✅ Found: $($envFile.Path)" $Colors.Green
    }
}

# Start infrastructure services
Write-ColorOutput "`n🗄️ Starting infrastructure services..." $Colors.Blue

try {
    docker-compose up -d postgres redis
    Write-ColorOutput "✅ Infrastructure services started" $Colors.Green
} catch {
    Write-ColorOutput "❌ Failed to start infrastructure services" $Colors.Red
    Write-ColorOutput "Error: $_" $Colors.Red
    exit 1
}

# Wait for database
if (!(Wait-ForService "PostgreSQL" "docker-compose exec -T postgres pg_isready -U chartgenius -d chartgenius_dev")) {
    Write-ColorOutput "❌ Database failed to start" $Colors.Red
    Write-ColorOutput "Checking logs..." $Colors.Yellow
    docker-compose logs postgres
    exit 1
}

# Wait for Redis
if (!(Wait-ForService "Redis" "docker-compose exec -T redis redis-cli ping")) {
    Write-ColorOutput "❌ Redis failed to start" $Colors.Red
    Write-ColorOutput "Checking logs..." $Colors.Yellow
    docker-compose logs redis
    exit 1
}

# Start application services
Write-ColorOutput "`n🚀 Starting application services..." $Colors.Blue

try {
    docker-compose up -d backend-v2 bot-v2
    Write-ColorOutput "✅ Application services started" $Colors.Green
} catch {
    Write-ColorOutput "❌ Failed to start application services" $Colors.Red
    Write-ColorOutput "Error: $_" $Colors.Red
    exit 1
}

# Wait for backend
if (!(Wait-ForService "Backend" "Invoke-WebRequest -Uri 'http://localhost:8001/health' -UseBasicParsing -TimeoutSec 5")) {
    Write-ColorOutput "❌ Backend failed to start" $Colors.Red
    Write-ColorOutput "Checking logs..." $Colors.Yellow
    docker-compose logs backend-v2
    exit 1
}

# Start frontend
Write-ColorOutput "`n🌐 Starting frontend..." $Colors.Blue

try {
    docker-compose up -d frontend-v2
    Write-ColorOutput "✅ Frontend started" $Colors.Green
} catch {
    Write-ColorOutput "❌ Failed to start frontend" $Colors.Red
    Write-ColorOutput "Error: $_" $Colors.Red
    exit 1
}

# Start monitoring services
Write-ColorOutput "`n📊 Starting monitoring services..." $Colors.Blue

try {
    docker-compose up -d prometheus grafana celery-flower
    Write-ColorOutput "✅ Monitoring services started" $Colors.Green
} catch {
    Write-ColorOutput "❌ Failed to start monitoring services" $Colors.Red
    Write-ColorOutput "Error: $_" $Colors.Red
    exit 1
}

# Start reverse proxy
Write-ColorOutput "`n🔀 Starting reverse proxy..." $Colors.Blue

try {
    docker-compose up -d nginx
    Write-ColorOutput "✅ Reverse proxy started" $Colors.Green
} catch {
    Write-ColorOutput "❌ Failed to start reverse proxy" $Colors.Red
    Write-ColorOutput "Error: $_" $Colors.Red
    exit 1
}

# Wait for services to settle
Start-Sleep -Seconds 5

# Health check
if (!$SkipHealthCheck) {
    Write-ColorOutput "`n🔍 Running health checks..." $Colors.Blue
    
    $services = @{
        "Frontend" = "http://localhost:3001"
        "Backend API" = "http://localhost:8001"
        "Backend Health" = "http://localhost:8001/health"
        "Grafana" = "http://localhost:3000"
        "Prometheus" = "http://localhost:9090"
    }
    
    $healthyServices = 0
    $totalServices = $services.Count
    
    foreach ($service in $services.GetEnumerator()) {
        try {
            $response = Invoke-WebRequest -Uri $service.Value -UseBasicParsing -TimeoutSec 10
            if ($response.StatusCode -eq 200) {
                Write-ColorOutput "  ✅ $($service.Key)" $Colors.Green
                $healthyServices++
            } else {
                Write-ColorOutput "  ❌ $($service.Key) (Status: $($response.StatusCode))" $Colors.Red
            }
        } catch {
            Write-ColorOutput "  ❌ $($service.Key) (Error: $($_.Exception.Message))" $Colors.Red
        }
    }
    
    Write-ColorOutput "`nHealth Check: $healthyServices/$totalServices services healthy" $Colors.Blue
}

# Show service status
Write-ColorOutput "`n🔍 Service Status:" $Colors.Blue
try {
    docker-compose ps
} catch {
    Write-ColorOutput "Failed to get service status" $Colors.Red
}

# Success message
Write-ColorOutput "`n🎉 ChartGenius v2 Development Environment Started!" $Colors.Green
Write-ColorOutput "==================================================" $Colors.Green

Write-ColorOutput "`n📱 Services:" $Colors.Blue
Write-ColorOutput "  Frontend:     http://localhost:3001" $Colors.Cyan
Write-ColorOutput "  Backend API:  http://localhost:8001" $Colors.Cyan
Write-ColorOutput "  API Docs:     http://localhost:8001/docs" $Colors.Cyan
Write-ColorOutput "  Bot Webhook:  http://localhost:8080" $Colors.Cyan

Write-ColorOutput "`n📊 Monitoring:" $Colors.Blue
Write-ColorOutput "  Grafana:      http://localhost:3000 (admin/grafana_dev_pass)" $Colors.Cyan
Write-ColorOutput "  Prometheus:   http://localhost:9090" $Colors.Cyan
Write-ColorOutput "  Flower:       http://localhost:5555 (admin/flower_dev_pass)" $Colors.Cyan

Write-ColorOutput "`n🗄️ Databases:" $Colors.Blue
Write-ColorOutput "  PostgreSQL:   localhost:5432 (chartgenius/chartgenius_dev_pass)" $Colors.Cyan
Write-ColorOutput "  Redis:        localhost:6379" $Colors.Cyan

Write-ColorOutput "`n📋 Useful commands:" $Colors.Blue
Write-ColorOutput "  View logs:    docker-compose logs -f [service]" $Colors.Cyan
Write-ColorOutput "  Stop all:     docker-compose down" $Colors.Cyan
Write-ColorOutput "  Restart:      docker-compose restart [service]" $Colors.Cyan
Write-ColorOutput "  Health check: .\scripts\health-check.ps1" $Colors.Cyan

Write-ColorOutput "`n💡 Tip: Use 'docker-compose logs -f' to monitor all services" $Colors.Yellow
Write-ColorOutput "`n🚀 Ready for development!" $Colors.Green
