#!/bin/bash

# ChartGenius v2 Development Startup Script
# Запуск полной инфраструктуры разработки

set -e

echo "🚀 Starting ChartGenius v2 Development Environment"
echo "=================================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    echo -e "${RED}❌ Docker is not running. Please start Docker first.${NC}"
    exit 1
fi

# Check if docker-compose is available
if ! command -v docker-compose &> /dev/null; then
    echo -e "${RED}❌ docker-compose is not installed.${NC}"
    exit 1
fi

# Create necessary directories
echo -e "${BLUE}📁 Creating directories...${NC}"
mkdir -p logs
mkdir -p data/postgres
mkdir -p data/redis
mkdir -p data/grafana
mkdir -p data/prometheus

# Set permissions
chmod -R 755 logs data

# Copy environment files if they don't exist
echo -e "${BLUE}📄 Setting up environment files...${NC}"

if [ ! -f backend-v2/.env.development ]; then
    echo -e "${YELLOW}⚠️  backend-v2/.env.development not found, creating from template...${NC}"
    cp backend-v2/.env.development.example backend-v2/.env.development 2>/dev/null || true
fi

if [ ! -f bot-v2/.env.development ]; then
    echo -e "${YELLOW}⚠️  bot-v2/.env.development not found, creating from template...${NC}"
    cp bot-v2/.env.development.example bot-v2/.env.development 2>/dev/null || true
fi

if [ ! -f frontend-v2/.env.development ]; then
    echo -e "${YELLOW}⚠️  frontend-v2/.env.development not found, creating from template...${NC}"
    cp frontend-v2/.env.development.example frontend-v2/.env.development 2>/dev/null || true
fi

# Start infrastructure services first
echo -e "${BLUE}🗄️  Starting infrastructure services...${NC}"
docker-compose up -d postgres redis

# Wait for database to be ready
echo -e "${BLUE}⏳ Waiting for database to be ready...${NC}"
timeout=60
counter=0
while ! docker-compose exec -T postgres pg_isready -U chartgenius -d chartgenius_dev > /dev/null 2>&1; do
    if [ $counter -ge $timeout ]; then
        echo -e "${RED}❌ Database failed to start within $timeout seconds${NC}"
        exit 1
    fi
    echo -n "."
    sleep 1
    ((counter++))
done
echo -e "${GREEN}✅ Database is ready${NC}"

# Wait for Redis to be ready
echo -e "${BLUE}⏳ Waiting for Redis to be ready...${NC}"
timeout=30
counter=0
while ! docker-compose exec -T redis redis-cli ping > /dev/null 2>&1; do
    if [ $counter -ge $timeout ]; then
        echo -e "${RED}❌ Redis failed to start within $timeout seconds${NC}"
        exit 1
    fi
    echo -n "."
    sleep 1
    ((counter++))
done
echo -e "${GREEN}✅ Redis is ready${NC}"

# Start application services
echo -e "${BLUE}🚀 Starting application services...${NC}"
docker-compose up -d backend-v2 bot-v2

# Wait for backend to be ready
echo -e "${BLUE}⏳ Waiting for backend to be ready...${NC}"
timeout=60
counter=0
while ! curl -f http://localhost:8001/health > /dev/null 2>&1; do
    if [ $counter -ge $timeout ]; then
        echo -e "${RED}❌ Backend failed to start within $timeout seconds${NC}"
        docker-compose logs backend-v2
        exit 1
    fi
    echo -n "."
    sleep 1
    ((counter++))
done
echo -e "${GREEN}✅ Backend is ready${NC}"

# Start frontend
echo -e "${BLUE}🌐 Starting frontend...${NC}"
docker-compose up -d frontend-v2

# Start monitoring services
echo -e "${BLUE}📊 Starting monitoring services...${NC}"
docker-compose up -d prometheus grafana celery-flower

# Start reverse proxy
echo -e "${BLUE}🔀 Starting reverse proxy...${NC}"
docker-compose up -d nginx

# Wait a bit for everything to settle
sleep 5

# Show status
echo -e "\n${GREEN}🎉 ChartGenius v2 Development Environment Started!${NC}"
echo "=================================================="
echo ""
echo -e "${BLUE}📱 Services:${NC}"
echo "  Frontend:     http://localhost:3001"
echo "  Backend API:  http://localhost:8001"
echo "  Bot Webhook:  http://localhost:8080"
echo ""
echo -e "${BLUE}📊 Monitoring:${NC}"
echo "  Grafana:      http://localhost:3000 (admin/grafana_dev_pass)"
echo "  Prometheus:   http://localhost:9090"
echo "  Flower:       http://localhost:5555 (admin/flower_dev_pass)"
echo ""
echo -e "${BLUE}🗄️  Databases:${NC}"
echo "  PostgreSQL:   localhost:5432 (chartgenius/chartgenius_dev_pass)"
echo "  Redis:        localhost:6379"
echo ""
echo -e "${BLUE}📋 Useful commands:${NC}"
echo "  View logs:    docker-compose logs -f [service]"
echo "  Stop all:     docker-compose down"
echo "  Restart:      docker-compose restart [service]"
echo "  Shell access: docker-compose exec [service] bash"
echo ""
echo -e "${YELLOW}💡 Tip: Use 'docker-compose logs -f' to monitor all services${NC}"

# Check service health
echo -e "\n${BLUE}🔍 Service Health Check:${NC}"
services=("frontend-v2" "backend-v2" "bot-v2" "postgres" "redis" "prometheus" "grafana")

for service in "${services[@]}"; do
    if docker-compose ps "$service" | grep -q "Up"; then
        echo -e "  ${GREEN}✅ $service${NC}"
    else
        echo -e "  ${RED}❌ $service${NC}"
    fi
done

echo ""
echo -e "${GREEN}🚀 Ready for development!${NC}"
