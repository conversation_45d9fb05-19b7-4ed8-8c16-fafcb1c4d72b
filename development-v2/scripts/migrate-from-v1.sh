#!/bin/bash

# ChartGenius Migration Script: v1 → v2
# Миграция данных и конфигураций с версии 1 на версию 2

set -e

echo "🔄 ChartGenius Migration: v1 → v2"
echo "================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
V1_DIR="../production"
V2_DIR="."
BACKUP_DIR="./migration-backup-$(date +%Y%m%d-%H%M%S)"

# Check if v1 directory exists
if [ ! -d "$V1_DIR" ]; then
    echo -e "${RED}❌ v1 directory not found: $V1_DIR${NC}"
    echo "Please ensure the v1 production directory exists"
    exit 1
fi

# Create backup directory
echo -e "${BLUE}📦 Creating backup directory...${NC}"
mkdir -p "$BACKUP_DIR"

# Backup current v2 configuration
echo -e "${BLUE}💾 Backing up current v2 configuration...${NC}"
cp -r backend-v2/.env* "$BACKUP_DIR/" 2>/dev/null || true
cp -r bot-v2/.env* "$BACKUP_DIR/" 2>/dev/null || true
cp -r frontend-v2/.env* "$BACKUP_DIR/" 2>/dev/null || true

echo -e "${GREEN}✅ Backup created: $BACKUP_DIR${NC}"

# Migration steps
echo -e "\n${BLUE}🔄 Starting migration process...${NC}"

# 1. Environment variables migration
echo -e "${BLUE}1️⃣  Migrating environment variables...${NC}"

# Backend environment
if [ -f "$V1_DIR/.env" ]; then
    echo -e "  📄 Migrating backend environment..."
    
    # Extract relevant variables from v1
    grep -E "^(TELEGRAM_BOT_TOKEN|OPENAI_API_KEY|DATABASE_URL|REDIS_URL|SECRET_KEY)" "$V1_DIR/.env" > "$BACKUP_DIR/v1-extracted.env" || true
    
    # Update v2 backend environment
    if [ -f "$BACKUP_DIR/v1-extracted.env" ]; then
        echo -e "  🔧 Updating backend-v2/.env.development..."
        
        # Read v1 variables
        source "$BACKUP_DIR/v1-extracted.env"
        
        # Update v2 environment file
        sed -i.bak "s|^TELEGRAM_BOT_TOKEN=.*|TELEGRAM_BOT_TOKEN=${TELEGRAM_BOT_TOKEN}|" backend-v2/.env.development
        sed -i.bak "s|^OPENAI_API_KEY=.*|OPENAI_API_KEY=${OPENAI_API_KEY}|" backend-v2/.env.development
        
        # Update bot environment
        sed -i.bak "s|^TELEGRAM_BOT_TOKEN=.*|TELEGRAM_BOT_TOKEN=${TELEGRAM_BOT_TOKEN}|" bot-v2/.env.development
        
        echo -e "  ${GREEN}✅ Environment variables migrated${NC}"
    else
        echo -e "  ${YELLOW}⚠️  No v1 environment variables found${NC}"
    fi
else
    echo -e "  ${YELLOW}⚠️  v1 .env file not found${NC}"
fi

# 2. Database migration
echo -e "\n${BLUE}2️⃣  Database migration...${NC}"

# Check if v1 database exists
if command -v psql &> /dev/null; then
    echo -e "  🗄️  Checking v1 database..."
    
    # Try to connect to v1 database
    V1_DB_URL=$(grep "^DATABASE_URL=" "$V1_DIR/.env" 2>/dev/null | cut -d'=' -f2- || echo "")
    
    if [ -n "$V1_DB_URL" ]; then
        echo -e "  📊 Exporting v1 data..."
        
        # Create data export directory
        mkdir -p "$BACKUP_DIR/data"
        
        # Export users
        echo "  📤 Exporting users..."
        psql "$V1_DB_URL" -c "COPY users TO STDOUT WITH CSV HEADER;" > "$BACKUP_DIR/data/users.csv" 2>/dev/null || true
        
        # Export other tables if they exist
        for table in subscriptions signals analyses; do
            echo "  📤 Exporting $table..."
            psql "$V1_DB_URL" -c "COPY $table TO STDOUT WITH CSV HEADER;" > "$BACKUP_DIR/data/$table.csv" 2>/dev/null || true
        done
        
        echo -e "  ${GREEN}✅ Data exported to $BACKUP_DIR/data/${NC}"
        
        # Create migration SQL script
        cat > "$BACKUP_DIR/migrate-data.sql" << 'EOF'
-- ChartGenius v1 to v2 Data Migration Script
-- Run this after starting v2 database

-- Import users (adjust column mapping as needed)
\copy users(tg_id,email,role,tier,first_name,last_name,username,created_at) FROM 'users.csv' WITH CSV HEADER;

-- Import subscriptions
\copy subscriptions(user_id,tier,price,exp_ts,source,created_at) FROM 'subscriptions.csv' WITH CSV HEADER;

-- Import signals
\copy signals(symbol,tf,text_md,ts,signal_type,confidence,created_at) FROM 'signals.csv' WITH CSV HEADER;

-- Import analyses
\copy analyses(user_id,symbol,timeframe,content,created_at) FROM 'analyses.csv' WITH CSV HEADER;

-- Update sequences
SELECT setval('users_id_seq', (SELECT MAX(id) FROM users));
SELECT setval('subscriptions_id_seq', (SELECT MAX(id) FROM subscriptions));
SELECT setval('signals_id_seq', (SELECT MAX(id) FROM signals));
SELECT setval('analyses_id_seq', (SELECT MAX(id) FROM analyses));
EOF
        
        echo -e "  📝 Migration script created: $BACKUP_DIR/migrate-data.sql"
        
    else
        echo -e "  ${YELLOW}⚠️  v1 database URL not found${NC}"
    fi
else
    echo -e "  ${YELLOW}⚠️  psql not available, skipping database export${NC}"
fi

# 3. Configuration files migration
echo -e "\n${BLUE}3️⃣  Migrating configuration files...${NC}"

# Copy SSL certificates if they exist
if [ -d "$V1_DIR/ssl" ]; then
    echo -e "  🔐 Copying SSL certificates..."
    cp -r "$V1_DIR/ssl" nginx/ 2>/dev/null || true
    echo -e "  ${GREEN}✅ SSL certificates copied${NC}"
fi

# Copy custom configurations
if [ -f "$V1_DIR/nginx.conf" ]; then
    echo -e "  🔧 Backing up v1 nginx configuration..."
    cp "$V1_DIR/nginx.conf" "$BACKUP_DIR/v1-nginx.conf"
fi

# 4. Create migration summary
echo -e "\n${BLUE}4️⃣  Creating migration summary...${NC}"

cat > "$BACKUP_DIR/MIGRATION_SUMMARY.md" << EOF
# ChartGenius v1 → v2 Migration Summary

**Migration Date**: $(date)
**Backup Location**: $BACKUP_DIR

## Migrated Components

### ✅ Environment Variables
- Telegram Bot Token
- OpenAI API Key
- Database URLs
- Redis URLs
- Secret Keys

### ✅ Database Export
- Users data exported to: data/users.csv
- Subscriptions data exported to: data/subscriptions.csv
- Signals data exported to: data/signals.csv
- Analyses data exported to: data/analyses.csv

### ✅ Configuration Files
- SSL certificates (if present)
- Nginx configuration backup

## Next Steps

1. **Start v2 environment**:
   \`\`\`bash
   ./scripts/start-dev.sh
   \`\`\`

2. **Import data** (if database export was successful):
   \`\`\`bash
   # Copy data files to container
   docker cp $BACKUP_DIR/data/ development-v2_postgres_1:/tmp/
   
   # Run migration script
   docker-compose exec postgres psql -U chartgenius -d chartgenius_dev -f /tmp/migrate-data.sql
   \`\`\`

3. **Verify migration**:
   - Check user count: http://localhost:8001/admin/stats
   - Test bot functionality
   - Verify WebApp access

4. **Update DNS** (for production):
   - Point domain to new v2 infrastructure
   - Update webhook URLs
   - Monitor logs for issues

## Rollback Plan

If issues occur, restore v1:
1. Stop v2: \`./scripts/stop-dev.sh\`
2. Restore v1 environment from backup
3. Update DNS back to v1
4. Investigate issues before retry

## Support

For migration issues, contact: <EMAIL>
EOF

echo -e "${GREEN}✅ Migration summary created: $BACKUP_DIR/MIGRATION_SUMMARY.md${NC}"

# 5. Final instructions
echo -e "\n${GREEN}🎉 Migration preparation complete!${NC}"
echo "================================="
echo ""
echo -e "${BLUE}📋 Next steps:${NC}"
echo "1. Review migration summary: $BACKUP_DIR/MIGRATION_SUMMARY.md"
echo "2. Start v2 environment: ./scripts/start-dev.sh"
echo "3. Import data (if exported): follow instructions in summary"
echo "4. Test all functionality before switching production"
echo ""
echo -e "${YELLOW}⚠️  Important:${NC}"
echo "- Keep v1 running until v2 is fully tested"
echo "- Backup is stored in: $BACKUP_DIR"
echo "- Review all configurations before production switch"
echo ""
echo -e "${BLUE}💡 Tip: Test the migration in a staging environment first${NC}"
