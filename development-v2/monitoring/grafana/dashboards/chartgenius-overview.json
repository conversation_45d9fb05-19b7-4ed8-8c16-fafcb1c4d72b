{"dashboard": {"id": null, "title": "ChartGenius v2 Overview", "tags": ["<PERSON><PERSON><PERSON>", "overview"], "style": "dark", "timezone": "browser", "panels": [{"id": 1, "title": "System Health", "type": "stat", "targets": [{"expr": "up{job=~\"chartgenius.*\"}", "legendFormat": "{{job}}"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "red", "value": 0}, {"color": "green", "value": 1}]}}}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}}, {"id": 2, "title": "HTTP Requests Rate", "type": "graph", "targets": [{"expr": "rate(http_requests_total[5m])", "legendFormat": "{{method}} {{endpoint}}"}], "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0}}, {"id": 3, "title": "Response Time (95th percentile)", "type": "graph", "targets": [{"expr": "histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m]))", "legendFormat": "{{endpoint}}"}], "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}}, {"id": 4, "title": "Error Rate", "type": "graph", "targets": [{"expr": "rate(http_requests_total{status_code=~\"5..\"}[5m])", "legendFormat": "{{endpoint}}"}], "gridPos": {"h": 8, "w": 12, "x": 12, "y": 8}}, {"id": 5, "title": "LLM Requests", "type": "graph", "targets": [{"expr": "rate(llm_requests_total[5m])", "legendFormat": "{{provider}} {{status}}"}], "gridPos": {"h": 8, "w": 12, "x": 0, "y": 16}}, {"id": 6, "title": "User Actions", "type": "graph", "targets": [{"expr": "rate(user_actions_total[5m])", "legendFormat": "{{action}} {{user_tier}}"}], "gridPos": {"h": 8, "w": 12, "x": 12, "y": 16}}], "time": {"from": "now-1h", "to": "now"}, "refresh": "30s"}}