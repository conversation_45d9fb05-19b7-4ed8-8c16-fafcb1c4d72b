# ChartGenius v2 Grafana Datasources
# Prometheus datasource configuration

apiVersion: 1

datasources:
  - name: Prometheus
    type: prometheus
    access: proxy
    url: http://prometheus:9090
    isDefault: true
    editable: true
    jsonData:
      timeInterval: "15s"
      queryTimeout: "60s"
      httpMethod: "POST"
    secureJsonData: {}
    version: 1

  - name: Prometheus-Alertmanager
    type: prometheus
    access: proxy
    url: http://alertmanager:9093
    isDefault: false
    editable: true
    jsonData:
      timeInterval: "30s"
    version: 1
