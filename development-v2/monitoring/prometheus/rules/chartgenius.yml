# ChartGenius v2 Prometheus Alert Rules
# Custom alerting rules for ChartGenius application

groups:
  - name: chartgenius.rules
    rules:
      # High error rate
      - alert: HighErrorRate
        expr: rate(http_requests_total{status_code=~"5.."}[5m]) > 0.1
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "High error rate detected"
          description: "Error rate is {{ $value }} errors per second for {{ $labels.job }}"

      # High response time
      - alert: HighResponseTime
        expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m])) > 2
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High response time detected"
          description: "95th percentile response time is {{ $value }}s for {{ $labels.job }}"

      # Database connection issues
      - alert: DatabaseConnectionFailure
        expr: database_queries_total{status="error"} > 10
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Database connection issues"
          description: "Database error count is {{ $value }} for {{ $labels.job }}"

      # Redis connection issues
      - alert: RedisConnectionFailure
        expr: redis_operations_total{status="error"} > 5
        for: 1m
        labels:
          severity: warning
        annotations:
          summary: "Redis connection issues"
          description: "Redis error count is {{ $value }} for {{ $labels.job }}"

      # LLM service failures
      - alert: LLMServiceFailure
        expr: llm_requests_total{status="error"} > 5
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "LLM service failures"
          description: "LLM error count is {{ $value }} for provider {{ $labels.provider }}"

      # High memory usage
      - alert: HighMemoryUsage
        expr: (process_resident_memory_bytes / 1024 / 1024) > 512
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High memory usage"
          description: "Memory usage is {{ $value }}MB for {{ $labels.job }}"

      # Service down
      - alert: ServiceDown
        expr: up == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Service is down"
          description: "{{ $labels.job }} service is down"

  - name: business.rules
    rules:
      # Low user activity
      - alert: LowUserActivity
        expr: rate(user_actions_total[1h]) < 1
        for: 30m
        labels:
          severity: info
        annotations:
          summary: "Low user activity"
          description: "User activity rate is {{ $value }} actions per second"

      # Payment failures
      - alert: PaymentFailures
        expr: rate(payment_transactions_total{status="failed"}[5m]) > 0.1
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "Payment failures detected"
          description: "Payment failure rate is {{ $value }} per second"

      # Signal generation issues
      - alert: SignalGenerationIssues
        expr: rate(signals_generated_total[10m]) < 0.01
        for: 15m
        labels:
          severity: warning
        annotations:
          summary: "Low signal generation rate"
          description: "Signal generation rate is {{ $value }} per second"
