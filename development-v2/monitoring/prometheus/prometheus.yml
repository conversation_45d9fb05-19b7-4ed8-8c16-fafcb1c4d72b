# ChartGenius v2 Prometheus Configuration
# Metrics collection for development environment

global:
  scrape_interval: 15s
  evaluation_interval: 15s
  external_labels:
    cluster: 'chartgenius-dev'
    environment: 'development'

# Rule files
rule_files:
  - "rules/*.yml"

# Scrape configurations
scrape_configs:
  # Prometheus itself
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']
    scrape_interval: 30s
    metrics_path: /metrics

  # ChartGenius Backend v2
  - job_name: 'chartgenius-backend-v2'
    static_configs:
      - targets: ['backend-v2:8001']
    scrape_interval: 15s
    metrics_path: /metrics
    scrape_timeout: 10s
    honor_labels: true

  # ChartGenius Bot v2
  - job_name: 'chartgenius-bot-v2'
    static_configs:
      - targets: ['bot-v2:8080']
    scrape_interval: 30s
    metrics_path: /metrics
    scrape_timeout: 10s

  # PostgreSQL Exporter
  - job_name: 'postgres'
    static_configs:
      - targets: ['postgres-exporter:9187']
    scrape_interval: 30s
    metrics_path: /metrics

  # Redis Exporter
  - job_name: 'redis'
    static_configs:
      - targets: ['redis-exporter:9121']
    scrape_interval: 30s
    metrics_path: /metrics

  # Node Exporter (system metrics)
  - job_name: 'node'
    static_configs:
      - targets: ['node-exporter:9100']
    scrape_interval: 30s
    metrics_path: /metrics

  # Nginx metrics
  - job_name: 'nginx'
    static_configs:
      - targets: ['nginx-exporter:9113']
    scrape_interval: 30s
    metrics_path: /metrics

  # Grafana metrics
  - job_name: 'grafana'
    static_configs:
      - targets: ['grafana:3000']
    scrape_interval: 60s
    metrics_path: /metrics

  # Celery metrics (via flower)
  - job_name: 'celery'
    static_configs:
      - targets: ['celery-flower:5555']
    scrape_interval: 30s
    metrics_path: /metrics

# Alerting configuration
alerting:
  alertmanagers:
    - static_configs:
        - targets:
          # - alertmanager:9093

# Storage configuration
storage:
  tsdb:
    retention.time: 7d
    retention.size: 1GB
