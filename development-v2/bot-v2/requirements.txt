# ChartGenius Bot v2 Dependencies
# aiogram 3.4 and related packages

# Core bot framework
aiogram==3.4.1
aiohttp==3.9.5
aiofiles==24.1.0

# HTTP client for backend communication
httpx==0.27.0

# Redis for state management and caching
redis==5.0.7
aioredis==2.0.1

# Configuration and environment
pydantic==2.8.0
pydantic-settings==2.4.0
python-dotenv==1.0.1

# Logging and monitoring
structlog==24.2.0
prometheus-client==0.20.0

# Date and time handling
python-dateutil==2.9.0

# JSON handling
orjson==3.10.6

# Image processing for charts
Pillow==10.4.0

# Development dependencies
pytest==8.2.2
pytest-asyncio==0.23.7
pytest-cov==5.0.0
black==24.4.2
isort==5.13.2
flake8==7.1.0
mypy==1.10.1
