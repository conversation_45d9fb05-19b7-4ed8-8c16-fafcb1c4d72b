#!/usr/bin/env python3
"""
ChartGenius v2 Local Bot
Simple Telegram bot for local testing
"""

import asyncio
import aiohttp
import json
from datetime import datetime
from typing import Dict, Any

# Simple bot implementation without aiogram for quick testing
class SimpleTelegramBot:
    def __init__(self, token: str, backend_url: str):
        self.token = token
        self.backend_url = backend_url
        self.base_url = f"https://api.telegram.org/bot{token}"
        
    async def send_message(self, chat_id: int, text: str, parse_mode: str = "HTML"):
        """Send message to Telegram"""
        url = f"{self.base_url}/sendMessage"
        data = {
            "chat_id": chat_id,
            "text": text,
            "parse_mode": parse_mode
        }

        async with aiohttp.ClientSession() as session:
            async with session.post(url, json=data) as response:
                return await response.json()

    async def send_message_with_keyboard(self, chat_id: int, text: str, keyboard: dict, parse_mode: str = "HTML"):
        """Send message with inline keyboard to Telegram"""
        url = f"{self.base_url}/sendMessage"
        data = {
            "chat_id": chat_id,
            "text": text,
            "parse_mode": parse_mode,
            "reply_markup": keyboard
        }

        async with aiohttp.ClientSession() as session:
            async with session.post(url, json=data) as response:
                return await response.json()
    
    async def get_updates(self, offset: int = 0):
        """Get updates from Telegram"""
        url = f"{self.base_url}/getUpdates"
        params = {"offset": offset, "timeout": 30}
        
        async with aiohttp.ClientSession() as session:
            async with session.get(url, params=params) as response:
                return await response.json()
    
    async def get_backend_data(self, endpoint: str):
        """Get data from backend API"""
        url = f"{self.backend_url}{endpoint}"
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(url) as response:
                    if response.status == 200:
                        return await response.json()
                    else:
                        return {"error": f"Backend returned {response.status}"}
        except Exception as e:
            return {"error": f"Backend connection failed: {str(e)}"}
    
    async def format_signals(self, signals_data: Dict[str, Any]) -> str:
        """Format signals for Telegram message"""
        if "error" in signals_data:
            return f"❌ <b>Error:</b> {signals_data['error']}"
        
        signals = signals_data.get("signals", [])
        if not signals:
            return "📊 <b>No active signals found</b>"
        
        message = "📊 <b>Active Trading Signals</b>\n\n"
        
        for signal in signals[:5]:  # Show max 5 signals
            signal_type = signal.get("signal_type", "UNKNOWN")
            symbol = signal.get("symbol", "UNKNOWN")
            confidence = signal.get("confidence", 0) * 100
            entry_price = signal.get("entry_price", 0)
            
            emoji = "🟢" if signal_type == "LONG" else "🔴" if signal_type == "SHORT" else "⚪"
            
            message += f"{emoji} <b>{symbol}</b> - {signal_type}\n"
            message += f"💰 Entry: ${entry_price:,.2f}\n"
            message += f"📈 Confidence: {confidence:.1f}%\n"
            message += f"⏰ {signal.get('timeframe', '1h')}\n\n"
        
        message += f"📊 Total signals: {signals_data.get('total', 0)}\n"
        message += f"🕐 Updated: {datetime.now().strftime('%H:%M:%S')}"
        
        return message
    
    async def format_analysis(self, analysis_data: Dict[str, Any], symbol: str) -> str:
        """Format analysis for Telegram message"""
        if "error" in analysis_data:
            return f"❌ <b>Error:</b> {analysis_data['error']}"
        
        analysis = analysis_data.get("analysis", {})
        indicators = analysis.get("technical_indicators", {})
        
        trend = analysis.get("trend", "unknown")
        trend_emoji = "📈" if trend == "bullish" else "📉" if trend == "bearish" else "➡️"
        
        recommendation = analysis.get("recommendation", "HOLD")
        rec_emoji = "🟢" if recommendation == "LONG" else "🔴" if recommendation == "SHORT" else "🟡"
        
        message = f"📈 <b>Analysis for {symbol}</b>\n\n"
        message += f"{trend_emoji} <b>Trend:</b> {trend.title()}\n"
        message += f"{rec_emoji} <b>Recommendation:</b> {recommendation}\n\n"
        
        message += f"📊 <b>Technical Indicators:</b>\n"
        message += f"• RSI: {indicators.get('rsi', 'N/A')}\n"
        message += f"• MACD: {indicators.get('macd', {}).get('signal', 'N/A')}\n"
        message += f"• Volume: {indicators.get('volume_analysis', 'N/A')}\n\n"
        
        support_levels = analysis.get("support_levels", [])
        resistance_levels = analysis.get("resistance_levels", [])
        
        if support_levels:
            message += f"🛡️ <b>Support:</b> {', '.join(map(str, support_levels[:3]))}\n"
        if resistance_levels:
            message += f"⚡ <b>Resistance:</b> {', '.join(map(str, resistance_levels[:3]))}\n"
        
        confidence = analysis_data.get("confidence", 0) * 100
        message += f"\n🎯 <b>Confidence:</b> {confidence:.1f}%"
        
        return message
    
    async def handle_message(self, message: Dict[str, Any]):
        """Handle incoming message"""
        chat_id = message["chat"]["id"]
        text = message.get("text", "").lower().strip()
        
        print(f"📨 Received message: '{text}' from chat {chat_id}")
        
        if text in ["/start", "/help"]:
            # Create inline keyboard with WebApp button
            keyboard = {
                "inline_keyboard": [
                    [
                        {
                            "text": "🚀 Открыть ChartGenius WebApp",
                            "web_app": {
                                "url": "http://localhost:3001"
                            }
                        }
                    ],
                    [
                        {
                            "text": "📊 Получить сигналы",
                            "callback_data": "get_signals"
                        },
                        {
                            "text": "📈 Анализ BTC",
                            "callback_data": "analysis_btc"
                        }
                    ]
                ]
            }

            welcome_text = """
🚀 <b>ChartGenius v2 Bot</b>

Добро пожаловать в систему криптоанализа!

<b>🌐 WebApp:</b> Нажмите кнопку ниже для открытия полного интерфейса

<b>📱 Команды бота:</b>
/signals - Активные торговые сигналы
/analysis SYMBOL - Анализ криптовалюты
/health - Статус системы
/webapp - Открыть WebApp

<b>Пример:</b> /analysis BTCUSDT
            """

            await self.send_message_with_keyboard(chat_id, welcome_text, keyboard)
            
        elif text == "/signals":
            signals_data = await self.get_backend_data("/api/v2/signals/")
            formatted_message = await self.format_signals(signals_data)
            await self.send_message(chat_id, formatted_message)
            
        elif text.startswith("/analysis"):
            parts = text.split()
            if len(parts) > 1:
                symbol = parts[1].upper()
                analysis_data = await self.get_backend_data(f"/api/v2/analysis/{symbol}")
                formatted_message = await self.format_analysis(analysis_data, symbol)
                await self.send_message(chat_id, formatted_message)
            else:
                await self.send_message(chat_id, "❌ Please specify a symbol: /analysis BTCUSDT")
                
        elif text == "/webapp":
            keyboard = {
                "inline_keyboard": [
                    [
                        {
                            "text": "🚀 Открыть ChartGenius WebApp",
                            "web_app": {
                                "url": "http://localhost:3001"
                            }
                        }
                    ]
                ]
            }
            await self.send_message_with_keyboard(
                chat_id,
                "🌐 <b>ChartGenius WebApp</b>\n\nНажмите кнопку ниже для открытия полного интерфейса с графиками и анализом:",
                keyboard
            )

        elif text == "/health":
            health_data = await self.get_backend_data("/health")
            if "error" in health_data:
                message = f"❌ <b>Backend Error:</b> {health_data['error']}"
            else:
                services = health_data.get("services", {})
                message = f"""
🔍 <b>System Health Check</b>

✅ <b>Backend:</b> {health_data.get('status', 'unknown')}
✅ <b>Redis:</b> {services.get('redis', 'unknown')}
✅ <b>Environment:</b> {health_data.get('environment', 'unknown')}
🕐 <b>Timestamp:</b> {datetime.now().strftime('%H:%M:%S')}
                """
            await self.send_message(chat_id, message)
            
        else:
            await self.send_message(chat_id, "❓ Unknown command. Type /help for available commands.")
    
    async def run_polling(self):
        """Run bot in polling mode"""
        print("🤖 Starting ChartGenius v2 Bot (Local Mode)")
        print(f"🔗 Backend URL: {self.backend_url}")
        print("📱 Bot is running in polling mode...")
        
        offset = 0
        
        while True:
            try:
                updates = await self.get_updates(offset)
                
                if updates.get("ok") and updates.get("result"):
                    for update in updates["result"]:
                        offset = update["update_id"] + 1
                        
                        if "message" in update:
                            await self.handle_message(update["message"])
                
                await asyncio.sleep(1)  # Small delay to prevent API spam
                
            except KeyboardInterrupt:
                print("\n🛑 Bot stopped by user")
                break
            except Exception as e:
                print(f"❌ Error in bot polling: {e}")
                await asyncio.sleep(5)  # Wait before retrying

async def main():
    # Configuration
    BOT_TOKEN = "**********:AAERodVAje0VnifJmUJWeq0EM4FxMueXrB0"
    BACKEND_URL = "http://localhost:8001"
    
    # Test backend connection first
    bot = SimpleTelegramBot(BOT_TOKEN, BACKEND_URL)
    
    print("🔍 Testing backend connection...")
    health_data = await bot.get_backend_data("/health")
    
    if "error" in health_data:
        print(f"❌ Backend connection failed: {health_data['error']}")
        print("🔧 Please make sure the backend is running on http://localhost:8001")
        return
    
    print(f"✅ Backend connected successfully!")
    print(f"📊 Backend status: {health_data.get('status')}")
    print(f"🔧 Environment: {health_data.get('environment')}")
    
    # Start bot
    await bot.run_polling()

if __name__ == "__main__":
    print("🚀 ChartGenius v2 Local Bot Starting...")
    
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 Bot shutdown complete!")
    except Exception as e:
        print(f"❌ Fatal error: {e}")
