"""
ChartGenius Bot v2
Telegram bot based on aiogram 3.4 with WebApp support
"""

import asyncio
import logging
import sys
from typing import Optional

import structlog
from aiogram import <PERSON><PERSON>, Dispatcher
from aiogram.client.default import DefaultBotProperties
from aiogram.enums import ParseMode
from aiogram.fsm.storage.redis import RedisStorage
from aiogram.webhook.aiohttp_server import SimpleRequestHandler, setup_application
from aiohttp import web
from aioredis import Redis

from app.config import get_settings
from app.handlers import setup_handlers
from app.middleware import setup_middleware
from app.services.backend_client import BackendClient
from app.utils.logging import setup_logging

# Setup structured logging
setup_logging()
logger = structlog.get_logger(__name__)

settings = get_settings()


async def create_bot() -> Bot:
    """Create and configure bot instance"""
    bot = Bot(
        token=settings.TELEGRAM_BOT_TOKEN,
        default=DefaultBotProperties(
            parse_mode=ParseMode.MARKDOWN_V2,
            link_preview_is_disabled=True,
        )
    )
    
    # Set bot commands
    from aiogram.types import BotCommand, BotCommandScopeDefault
    
    commands = [
        BotCommand(command="start", description="🚀 Запустить бота"),
        BotCommand(command="help", description="❓ Помощь"),
        BotCommand(command="signals", description="📊 Торговые сигналы"),
        BotCommand(command="analysis", description="🔍 Анализ рынка"),
        BotCommand(command="portfolio", description="💼 Портфолио"),
        BotCommand(command="settings", description="⚙️ Настройки"),
        BotCommand(command="webapp", description="🌐 Открыть WebApp"),
    ]
    
    await bot.set_my_commands(commands, BotCommandScopeDefault())
    
    logger.info("Bot commands set successfully")
    return bot


async def create_dispatcher() -> Dispatcher:
    """Create and configure dispatcher"""
    
    # Setup Redis storage for FSM
    redis = Redis.from_url(settings.REDIS_URL)
    storage = RedisStorage(redis=redis)
    
    dp = Dispatcher(storage=storage)
    
    # Setup middleware
    setup_middleware(dp)
    
    # Setup handlers
    setup_handlers(dp)
    
    logger.info("Dispatcher configured successfully")
    return dp


async def on_startup(bot: Bot, backend_client: BackendClient) -> None:
    """Startup callback"""
    
    # Test backend connection
    try:
        health = await backend_client.get_health()
        logger.info("Backend connection successful", health=health)
    except Exception as e:
        logger.error("Backend connection failed", error=str(e))
    
    # Set webhook if in webhook mode
    if settings.WEBHOOK_MODE and settings.WEBHOOK_URL:
        webhook_url = f"{settings.WEBHOOK_URL}/webhook"
        await bot.set_webhook(
            url=webhook_url,
            secret_token=settings.WEBHOOK_SECRET,
            allowed_updates=["message", "callback_query", "inline_query"],
        )
        logger.info("Webhook set", url=webhook_url)
    else:
        # Delete webhook for polling mode
        await bot.delete_webhook(drop_pending_updates=True)
        logger.info("Webhook deleted, using polling mode")
    
    # Get bot info
    bot_info = await bot.get_me()
    logger.info(
        "Bot started successfully",
        bot_id=bot_info.id,
        bot_username=bot_info.username,
        webhook_mode=settings.WEBHOOK_MODE,
    )


async def on_shutdown(bot: Bot) -> None:
    """Shutdown callback"""
    
    if settings.WEBHOOK_MODE:
        await bot.delete_webhook()
        logger.info("Webhook deleted")
    
    await bot.session.close()
    logger.info("Bot session closed")


async def main() -> None:
    """Main function"""
    
    logger.info("Starting ChartGenius Bot v2", version="2.0.0-dev")
    
    # Create bot and dispatcher
    bot = await create_bot()
    dp = await create_dispatcher()
    
    # Create backend client
    backend_client = BackendClient(settings.BACKEND_URL)
    
    # Add backend client to dispatcher data
    dp["backend_client"] = backend_client
    
    # Setup startup and shutdown callbacks
    dp.startup.register(lambda: on_startup(bot, backend_client))
    dp.shutdown.register(lambda: on_shutdown(bot))
    
    try:
        if settings.WEBHOOK_MODE:
            # Webhook mode
            app = web.Application()
            
            # Setup webhook handler
            webhook_requests_handler = SimpleRequestHandler(
                dispatcher=dp,
                bot=bot,
                secret_token=settings.WEBHOOK_SECRET,
            )
            webhook_requests_handler.register(app, path="/webhook")
            
            # Setup application
            setup_application(app, dp, bot=bot)
            
            # Add health check endpoint
            async def health_check(request):
                return web.json_response({"status": "healthy", "version": "2.0.0-dev"})
            
            app.router.add_get("/health", health_check)
            
            # Add metrics endpoint
            async def metrics(request):
                from prometheus_client import generate_latest, CONTENT_TYPE_LATEST
                return web.Response(
                    text=generate_latest().decode(),
                    content_type=CONTENT_TYPE_LATEST,
                )
            
            app.router.add_get("/metrics", metrics)
            
            # Start webhook server
            runner = web.AppRunner(app)
            await runner.setup()
            
            site = web.TCPSite(
                runner,
                host=settings.WEBHOOK_HOST,
                port=settings.WEBHOOK_PORT,
            )
            
            await site.start()
            
            logger.info(
                "Webhook server started",
                host=settings.WEBHOOK_HOST,
                port=settings.WEBHOOK_PORT,
            )
            
            # Keep running
            try:
                await asyncio.Future()  # Run forever
            finally:
                await runner.cleanup()
        
        else:
            # Polling mode
            logger.info("Starting polling mode")
            await dp.start_polling(bot, allowed_updates=["message", "callback_query", "inline_query"])
    
    except KeyboardInterrupt:
        logger.info("Bot stopped by user")
    except Exception as e:
        logger.error("Bot error", error=str(e), exc_info=True)
        sys.exit(1)
    finally:
        await bot.session.close()
        logger.info("Bot shutdown complete")


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("Bot interrupted by user")
    except Exception as e:
        logger.error("Fatal error", error=str(e), exc_info=True)
        sys.exit(1)
