"""
Configuration for ChartGenius Bot v2
"""

import os
from functools import lru_cache
from typing import List, Optional

from pydantic import Field, validator
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """Bot settings"""
    
    # Application
    APP_NAME: str = "ChartGenius Bot"
    VERSION: str = "2.0.0-dev"
    DEBUG: bool = Field(default=False, env="DEBUG")
    ENVIRONMENT: str = Field(default="development", env="ENVIRONMENT")
    
    # Telegram Bot
    TELEGRAM_BOT_TOKEN: str = Field(..., env="TELEGRAM_BOT_TOKEN")
    TELEGRAM_BOT_USERNAME: str = Field(default="Chart_Genius_bot", env="TELEGRAM_BOT_USERNAME")
    
    # Webhook configuration
    WEBHOOK_MODE: bool = Field(default=False, env="WEBHOOK_MODE")
    WEBHOOK_HOST: str = Field(default="0.0.0.0", env="WEBHOOK_HOST")
    WEBHOOK_PORT: int = Field(default=8080, env="WEBHOOK_PORT")
    WEBHOOK_URL: Optional[str] = Field(default=None, env="WEBHOOK_URL")
    WEBHOOK_SECRET: Optional[str] = Field(default=None, env="WEBHOOK_SECRET")
    
    # Backend API
    BACKEND_URL: str = Field(default="http://localhost:8001/api/v2", env="BACKEND_URL")
    BACKEND_TIMEOUT: int = Field(default=30, env="BACKEND_TIMEOUT")
    
    # Redis
    REDIS_URL: str = Field(default="redis://localhost:6379/3", env="REDIS_URL")
    
    # WebApp
    WEBAPP_URL: str = Field(default="https://chartgenius.dev", env="WEBAPP_URL")
    
    # Rate limiting
    RATE_LIMIT_MESSAGES: int = Field(default=20, env="RATE_LIMIT_MESSAGES")
    RATE_LIMIT_WINDOW: int = Field(default=60, env="RATE_LIMIT_WINDOW")
    
    # Admin users
    ADMIN_USER_IDS: List[int] = Field(default=[299820674], env="ADMIN_USER_IDS")
    
    # Logging
    LOG_LEVEL: str = Field(default="INFO", env="LOG_LEVEL")
    
    # Features
    ENABLE_INLINE_MODE: bool = Field(default=True, env="ENABLE_INLINE_MODE")
    ENABLE_WEBAPP: bool = Field(default=True, env="ENABLE_WEBAPP")
    ENABLE_NOTIFICATIONS: bool = Field(default=True, env="ENABLE_NOTIFICATIONS")
    
    # Metrics
    ENABLE_METRICS: bool = Field(default=True, env="ENABLE_METRICS")
    METRICS_PORT: int = Field(default=8080, env="METRICS_PORT")
    
    @validator("ADMIN_USER_IDS", pre=True)
    def parse_admin_ids(cls, v):
        if isinstance(v, str):
            return [int(x.strip()) for x in v.split(",") if x.strip()]
        return v
    
    @validator("LOG_LEVEL")
    def validate_log_level(cls, v):
        valid_levels = ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]
        if v.upper() not in valid_levels:
            raise ValueError(f"LOG_LEVEL must be one of {valid_levels}")
        return v.upper()
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = True


@lru_cache()
def get_settings() -> Settings:
    """Get cached settings instance"""
    return Settings()


# Environment-specific configurations
class DevelopmentSettings(Settings):
    """Development environment settings"""
    DEBUG: bool = True
    LOG_LEVEL: str = "DEBUG"
    WEBHOOK_MODE: bool = False
    
    class Config:
        env_file = ".env.development"


class ProductionSettings(Settings):
    """Production environment settings"""
    DEBUG: bool = False
    LOG_LEVEL: str = "INFO"
    WEBHOOK_MODE: bool = True
    
    class Config:
        env_file = ".env.production"


def get_settings_for_environment(env: str = None) -> Settings:
    """Get settings for specific environment"""
    env = env or os.getenv("ENVIRONMENT", "development").lower()
    
    if env == "production":
        return ProductionSettings()
    else:
        return DevelopmentSettings()
