"""
Keyboard utilities for ChartGenius Bot v2
"""

from aiogram.types import (
    ReplyKeyboardMarkup, 
    KeyboardButton, 
    InlineKeyboardMarkup, 
    InlineKeyboardButton,
    WebAppInfo
)

from app.config import get_settings

settings = get_settings()


def get_main_keyboard(user_tier: str = "free") -> ReplyKeyboardMarkup:
    """Get main reply keyboard based on user tier"""
    
    buttons = [
        [KeyboardButton(text="📊 Сигналы"), KeyboardButton(text="🔍 Анализ")],
        [KeyboardButton(text="💼 Портфолио"), KeyboardButton(text="⚙️ Настройки")],
    ]
    
    # Add WebApp button for premium users
    if user_tier in ["premium", "vip"] and settings.ENABLE_WEBAPP:
        buttons.append([KeyboardButton(text="🌐 WebApp")])
    
    # Add help button
    buttons.append([KeyboardButton(text="❓ Помощь")])
    
    return ReplyKeyboardMarkup(
        keyboard=buttons,
        resize_keyboard=True,
        one_time_keyboard=False,
        input_field_placeholder="Выберите действие..."
    )


def get_webapp_keyboard() -> InlineKeyboardMarkup:
    """Get WebApp inline keyboard"""
    
    if not settings.ENABLE_WEBAPP:
        return InlineKeyboardMarkup(inline_keyboard=[])
    
    return InlineKeyboardMarkup(inline_keyboard=[
        [InlineKeyboardButton(
            text="🌐 Открыть WebApp",
            web_app=WebAppInfo(url=settings.WEBAPP_URL)
        )],
    ])


def get_signals_keyboard() -> InlineKeyboardMarkup:
    """Get signals inline keyboard"""
    
    return InlineKeyboardMarkup(inline_keyboard=[
        [
            InlineKeyboardButton(text="📊 Все сигналы", callback_data="signals_all"),
            InlineKeyboardButton(text="🔥 Топ сигналы", callback_data="signals_top"),
        ],
        [
            InlineKeyboardButton(text="₿ Bitcoin", callback_data="signals_BTCUSDT"),
            InlineKeyboardButton(text="Ξ Ethereum", callback_data="signals_ETHUSDT"),
        ],
        [
            InlineKeyboardButton(text="🌐 Открыть в WebApp", web_app=WebAppInfo(url=f"{settings.WEBAPP_URL}/signals")),
        ],
    ])


def get_analysis_keyboard() -> InlineKeyboardMarkup:
    """Get analysis inline keyboard"""
    
    return InlineKeyboardMarkup(inline_keyboard=[
        [
            InlineKeyboardButton(text="🔍 Новый анализ", callback_data="analysis_new"),
            InlineKeyboardButton(text="📋 История", callback_data="analysis_history"),
        ],
        [
            InlineKeyboardButton(text="₿ Анализ Bitcoin", callback_data="analysis_BTCUSDT"),
            InlineKeyboardButton(text="Ξ Анализ Ethereum", callback_data="analysis_ETHUSDT"),
        ],
        [
            InlineKeyboardButton(text="🌐 Открыть в WebApp", web_app=WebAppInfo(url=f"{settings.WEBAPP_URL}/analysis")),
        ],
    ])


def get_symbol_keyboard() -> InlineKeyboardMarkup:
    """Get symbol selection keyboard"""
    
    symbols = [
        ("₿ Bitcoin", "BTCUSDT"),
        ("Ξ Ethereum", "ETHUSDT"),
        ("🔗 Chainlink", "LINKUSDT"),
        ("🌕 Cardano", "ADAUSDT"),
        ("🔸 BNB", "BNBUSDT"),
        ("🦄 Uniswap", "UNIUSDT"),
    ]
    
    buttons = []
    for i in range(0, len(symbols), 2):
        row = []
        for j in range(2):
            if i + j < len(symbols):
                name, symbol = symbols[i + j]
                row.append(InlineKeyboardButton(
                    text=name,
                    callback_data=f"symbol_{symbol}"
                ))
        buttons.append(row)
    
    return InlineKeyboardMarkup(inline_keyboard=buttons)


def get_timeframe_keyboard() -> InlineKeyboardMarkup:
    """Get timeframe selection keyboard"""
    
    timeframes = [
        ("1m", "1m"), ("5m", "5m"), ("15m", "15m"),
        ("1h", "1h"), ("4h", "4h"), ("1d", "1d"),
    ]
    
    buttons = []
    for i in range(0, len(timeframes), 3):
        row = []
        for j in range(3):
            if i + j < len(timeframes):
                name, tf = timeframes[i + j]
                row.append(InlineKeyboardButton(
                    text=name,
                    callback_data=f"timeframe_{tf}"
                ))
        buttons.append(row)
    
    return InlineKeyboardMarkup(inline_keyboard=buttons)


def get_admin_keyboard() -> InlineKeyboardMarkup:
    """Get admin panel keyboard"""
    
    return InlineKeyboardMarkup(inline_keyboard=[
        [
            InlineKeyboardButton(text="📊 Статистика", callback_data="admin_stats"),
            InlineKeyboardButton(text="👥 Пользователи", callback_data="admin_users"),
        ],
        [
            InlineKeyboardButton(text="📢 Рассылка", callback_data="admin_broadcast"),
            InlineKeyboardButton(text="🤖 LLM статус", callback_data="admin_llm"),
        ],
        [
            InlineKeyboardButton(text="🌐 Админ панель", web_app=WebAppInfo(url=f"{settings.WEBAPP_URL}/admin")),
        ],
    ])


def get_pagination_keyboard(
    current_page: int,
    total_pages: int,
    callback_prefix: str
) -> InlineKeyboardMarkup:
    """Get pagination keyboard"""
    
    buttons = []
    
    # Navigation buttons
    nav_row = []
    
    if current_page > 1:
        nav_row.append(InlineKeyboardButton(
            text="⬅️ Назад",
            callback_data=f"{callback_prefix}_page_{current_page - 1}"
        ))
    
    nav_row.append(InlineKeyboardButton(
        text=f"{current_page}/{total_pages}",
        callback_data="noop"
    ))
    
    if current_page < total_pages:
        nav_row.append(InlineKeyboardButton(
            text="Вперед ➡️",
            callback_data=f"{callback_prefix}_page_{current_page + 1}"
        ))
    
    buttons.append(nav_row)
    
    # Quick jump buttons for large pagination
    if total_pages > 5:
        jump_row = []
        
        if current_page > 3:
            jump_row.append(InlineKeyboardButton(
                text="1",
                callback_data=f"{callback_prefix}_page_1"
            ))
        
        if current_page < total_pages - 2:
            jump_row.append(InlineKeyboardButton(
                text=str(total_pages),
                callback_data=f"{callback_prefix}_page_{total_pages}"
            ))
        
        if jump_row:
            buttons.append(jump_row)
    
    return InlineKeyboardMarkup(inline_keyboard=buttons)


def get_back_keyboard(callback_data: str = "back") -> InlineKeyboardMarkup:
    """Get simple back button keyboard"""
    
    return InlineKeyboardMarkup(inline_keyboard=[
        [InlineKeyboardButton(text="⬅️ Назад", callback_data=callback_data)],
    ])


def get_confirm_keyboard(
    confirm_callback: str,
    cancel_callback: str = "cancel"
) -> InlineKeyboardMarkup:
    """Get confirmation keyboard"""
    
    return InlineKeyboardMarkup(inline_keyboard=[
        [
            InlineKeyboardButton(text="✅ Да", callback_data=confirm_callback),
            InlineKeyboardButton(text="❌ Нет", callback_data=cancel_callback),
        ],
    ])
