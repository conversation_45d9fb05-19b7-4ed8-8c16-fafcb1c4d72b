"""
Handlers package for ChartGenius Bot v2
"""

from aiogram import Dispatcher

from .start import router as start_router
from .signals import router as signals_router
from .webapp import router as webapp_router
from .admin import router as admin_router
from .inline import router as inline_router


def setup_handlers(dp: Dispatcher) -> None:
    """Setup all handlers"""
    
    # Include routers in order of priority
    dp.include_router(start_router)
    dp.include_router(webapp_router)
    dp.include_router(signals_router)
    dp.include_router(admin_router)
    dp.include_router(inline_router)  # Inline mode should be last
