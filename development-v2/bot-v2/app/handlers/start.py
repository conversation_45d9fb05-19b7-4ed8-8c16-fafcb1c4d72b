"""
Start and basic command handlers
"""

import structlog
from aiogram import Router, F
from aiogram.filters import Command, CommandStart
from aiogram.types import Message, InlineKeyboardMarkup, InlineKeyboardButton, WebAppInfo
from aiogram.utils.markdown import hbold, hcode, hlink

from app.config import get_settings
from app.services.backend_client import BackendClient
from app.utils.keyboards import get_main_keyboard, get_webapp_keyboard
from app.utils.text import escape_markdown

logger = structlog.get_logger(__name__)
router = Router()
settings = get_settings()


@router.message(CommandStart())
async def start_handler(message: Message, backend_client: BackendClient):
    """Handle /start command"""
    
    user = message.from_user
    logger.info("Start command received", user_id=user.id, username=user.username)
    
    try:
        # Register/authenticate user with backend
        auth_data = {
            "telegram_id": str(user.id),
            "first_name": user.first_name,
            "last_name": user.last_name,
            "username": user.username,
            "language_code": user.language_code or "ru",
        }
        
        user_info = await backend_client.authenticate_user(auth_data)
        
        # Welcome message
        welcome_text = f"""
🚀 {hbold("Добро пожаловать в ChartGenius!")}

Привет, {escape_markdown(user.first_name or user.username or "трейдер")}\\!

ChartGenius — это ваш персональный помощник для анализа криптовалют с использованием искусственного интеллекта\\.

{hbold("Ваш статус:")} {escape_markdown(user_info.get('tier', 'free').title())}
{hbold("Роль:")} {escape_markdown(user_info.get('role', 'user').title())}

{hbold("Что я умею:")}
📊 Анализировать криптовалюты с помощью ИИ
🎯 Предоставлять торговые сигналы
📈 Отслеживать портфолио
🌐 Работать через удобный WebApp

{hbold("Команды:")}
/signals \\- Торговые сигналы
/analysis \\- Анализ рынка
/portfolio \\- Ваше портфолио
/webapp \\- Открыть WebApp
/help \\- Помощь

Выберите действие из меню ниже или используйте команды\\!
"""
        
        # Send welcome message with keyboard
        await message.answer(
            welcome_text,
            reply_markup=get_main_keyboard(user_info.get('tier', 'free')),
            parse_mode="MarkdownV2"
        )
        
        # If user has premium access, show WebApp button
        if user_info.get('tier') in ['premium', 'vip']:
            await message.answer(
                "🌐 Откройте полнофункциональный WebApp для удобной работы:",
                reply_markup=get_webapp_keyboard()
            )
        
    except Exception as e:
        logger.error("Error in start handler", error=str(e), user_id=user.id)
        
        # Fallback message
        await message.answer(
            "🚀 Добро пожаловать в ChartGenius\\!\n\n"
            "Произошла ошибка при подключении к серверу\\. "
            "Попробуйте позже или обратитесь в поддержку\\.",
            parse_mode="MarkdownV2"
        )


@router.message(Command("help"))
async def help_handler(message: Message):
    """Handle /help command"""
    
    help_text = f"""
❓ {hbold("Помощь по ChartGenius")}

{hbold("Основные команды:")}
/start \\- Главное меню
/signals \\- Торговые сигналы
/analysis \\- Анализ криптовалют
/portfolio \\- Управление портфолио
/webapp \\- Открыть WebApp
/settings \\- Настройки

{hbold("Тарифные планы:")}
🆓 {hbold("Free")} \\- Базовый анализ
💎 {hbold("Premium")} \\- Торговые сигналы \\+ анализ
👑 {hbold("VIP")} \\- Все функции \\+ приоритет

{hbold("WebApp функции:")}
📊 Интерактивные графики
🎯 Персональные сигналы
📈 Портфолио трекинг
⚙️ Расширенные настройки

{hbold("Поддержка:")}
Если у вас есть вопросы, напишите @chartgenius\\_support

{hbold("Версия:")} 2\\.0\\.0\\-dev
"""
    
    await message.answer(help_text, parse_mode="MarkdownV2")


@router.message(Command("webapp"))
async def webapp_handler(message: Message):
    """Handle /webapp command"""
    
    if not settings.ENABLE_WEBAPP:
        await message.answer("WebApp временно недоступен")
        return
    
    webapp_text = f"""
🌐 {hbold("ChartGenius WebApp")}

Откройте полнофункциональное веб\\-приложение для:

📊 Интерактивного анализа графиков
🎯 Просмотра торговых сигналов
📈 Управления портфолио
⚙️ Настройки аккаунта

WebApp работает прямо в Telegram и синхронизируется с ботом\\!
"""
    
    await message.answer(
        webapp_text,
        reply_markup=get_webapp_keyboard(),
        parse_mode="MarkdownV2"
    )


@router.message(Command("settings"))
async def settings_handler(message: Message, backend_client: BackendClient):
    """Handle /settings command"""
    
    user_id = str(message.from_user.id)
    
    try:
        # Get user settings from backend
        user_info = await backend_client.get_user_profile(user_id)
        
        settings_text = f"""
⚙️ {hbold("Настройки")}

{hbold("Профиль:")}
👤 Имя: {escape_markdown(user_info.get('first_name', 'Не указано'))}
🆔 ID: {hcode(user_id)}
🌍 Язык: {escape_markdown(user_info.get('language_code', 'ru').upper())}

{hbold("Подписка:")}
💎 Тариф: {escape_markdown(user_info.get('tier', 'free').title())}
📅 Активна до: {escape_markdown(user_info.get('subscription_expires', 'Не активна'))}

{hbold("Статистика:")}
📊 Анализов: {user_info.get('total_analyses', 0)}
🎯 Сигналов: {user_info.get('total_signals_received', 0)}

Для изменения настроек используйте WebApp или команды бота\\.
"""
        
        keyboard = InlineKeyboardMarkup(inline_keyboard=[
            [InlineKeyboardButton(text="🌐 Открыть настройки в WebApp", web_app=WebAppInfo(url=f"{settings.WEBAPP_URL}/settings"))],
            [InlineKeyboardButton(text="💎 Улучшить тариф", callback_data="upgrade_plan")],
        ])
        
        await message.answer(
            settings_text,
            reply_markup=keyboard,
            parse_mode="MarkdownV2"
        )
        
    except Exception as e:
        logger.error("Error getting user settings", error=str(e), user_id=user_id)
        await message.answer("Ошибка получения настроек. Попробуйте позже.")


@router.message(F.text == "📊 Сигналы")
async def signals_button_handler(message: Message):
    """Handle signals button"""
    await message.answer("Переходим к сигналам...")
    # Redirect to signals handler
    from .signals import signals_handler
    await signals_handler(message)


@router.message(F.text == "🔍 Анализ")
async def analysis_button_handler(message: Message):
    """Handle analysis button"""
    await message.answer("Открываем анализ рынка...")
    # Could redirect to analysis handler or WebApp


@router.message(F.text == "💼 Портфолио")
async def portfolio_button_handler(message: Message):
    """Handle portfolio button"""
    await message.answer("Открываем портфолио...")
    # Could redirect to portfolio handler or WebApp


@router.message(F.text == "🌐 WebApp")
async def webapp_button_handler(message: Message):
    """Handle WebApp button"""
    await webapp_handler(message)
