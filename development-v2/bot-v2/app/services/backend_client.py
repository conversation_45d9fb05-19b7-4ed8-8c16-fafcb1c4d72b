"""
Backend API client for ChartGenius Bot v2
"""

import asyncio
from typing import Dict, List, Optional, Any

import httpx
import structlog
from httpx import AsyncClient, HTTPStatusError, RequestError

logger = structlog.get_logger(__name__)


class BackendClientError(Exception):
    """Backend client error"""
    pass


class BackendClient:
    """HTTP client for backend API communication"""
    
    def __init__(self, base_url: str, timeout: int = 30):
        self.base_url = base_url.rstrip('/')
        self.timeout = timeout
        self._client: Optional[AsyncClient] = None
    
    async def _get_client(self) -> AsyncClient:
        """Get or create HTTP client"""
        if self._client is None:
            self._client = AsyncClient(
                base_url=self.base_url,
                timeout=self.timeout,
                headers={
                    "User-Agent": "ChartGenius-Bot/2.0.0",
                    "Content-Type": "application/json",
                }
            )
        return self._client
    
    async def close(self):
        """Close HTTP client"""
        if self._client:
            await self._client.aclose()
            self._client = None
    
    async def _request(
        self,
        method: str,
        endpoint: str,
        data: Optional[Dict] = None,
        params: Optional[Dict] = None,
        headers: Optional[Dict] = None,
    ) -> Dict[str, Any]:
        """Make HTTP request to backend"""
        
        client = await self._get_client()
        url = f"{endpoint}"
        
        try:
            response = await client.request(
                method=method,
                url=url,
                json=data,
                params=params,
                headers=headers or {},
            )
            
            response.raise_for_status()
            
            if response.headers.get("content-type", "").startswith("application/json"):
                return response.json()
            else:
                return {"data": response.text}
                
        except HTTPStatusError as e:
            logger.error(
                "Backend HTTP error",
                status_code=e.response.status_code,
                url=url,
                method=method,
                error=str(e),
            )
            
            try:
                error_data = e.response.json()
                raise BackendClientError(f"Backend error: {error_data.get('message', str(e))}")
            except:
                raise BackendClientError(f"Backend error: HTTP {e.response.status_code}")
        
        except RequestError as e:
            logger.error(
                "Backend request error",
                url=url,
                method=method,
                error=str(e),
            )
            raise BackendClientError(f"Backend connection error: {str(e)}")
        
        except Exception as e:
            logger.error(
                "Backend unexpected error",
                url=url,
                method=method,
                error=str(e),
                exc_info=True,
            )
            raise BackendClientError(f"Backend error: {str(e)}")
    
    # Health check
    async def get_health(self) -> Dict[str, Any]:
        """Get backend health status"""
        return await self._request("GET", "/health")
    
    # Authentication
    async def authenticate_user(self, auth_data: Dict[str, Any]) -> Dict[str, Any]:
        """Authenticate user with backend"""
        return await self._request("POST", "/auth/login", data=auth_data)
    
    async def get_user_profile(self, user_id: str) -> Dict[str, Any]:
        """Get user profile"""
        # Note: In real implementation, you'd need to handle authentication
        # For now, we'll use a simple approach
        return await self._request("GET", f"/users/{user_id}")
    
    # Signals
    async def get_signals(
        self,
        page: int = 1,
        size: int = 10,
        symbol: Optional[str] = None,
        timeframe: Optional[str] = None,
    ) -> Dict[str, Any]:
        """Get trading signals"""
        params = {"page": page, "size": size}
        if symbol:
            params["symbol"] = symbol
        if timeframe:
            params["tf"] = timeframe
        
        return await self._request("GET", "/signals/", params=params)
    
    async def get_signal(self, signal_id: int) -> Dict[str, Any]:
        """Get specific signal"""
        return await self._request("GET", f"/signals/{signal_id}")
    
    # Analysis
    async def create_analysis(self, analysis_data: Dict[str, Any]) -> Dict[str, Any]:
        """Create new analysis"""
        return await self._request("POST", "/analysis/create", data=analysis_data)
    
    async def get_analyses(self, user_id: str) -> Dict[str, Any]:
        """Get user analyses"""
        return await self._request("GET", "/analysis/", params={"user_id": user_id})
    
    # LLM
    async def chat_with_llm(self, messages: List[Dict[str, str]]) -> Dict[str, Any]:
        """Chat with LLM"""
        return await self._request("POST", "/llm/chat", data={"messages": messages})
    
    # Admin
    async def get_system_stats(self) -> Dict[str, Any]:
        """Get system statistics (admin only)"""
        return await self._request("GET", "/admin/stats")
    
    async def send_broadcast(self, message: str) -> Dict[str, Any]:
        """Send broadcast message (admin only)"""
        return await self._request("POST", "/admin/broadcast", data={"message": message})


# Global client instance
_backend_client: Optional[BackendClient] = None


def get_backend_client(base_url: str) -> BackendClient:
    """Get global backend client instance"""
    global _backend_client
    
    if _backend_client is None:
        _backend_client = BackendClient(base_url)
    
    return _backend_client


async def close_backend_client():
    """Close global backend client"""
    global _backend_client
    
    if _backend_client:
        await _backend_client.close()
        _backend_client = None
