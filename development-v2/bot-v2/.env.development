# ChartGenius Bot v2 Development Environment

# Application
DEBUG=true
ENVIRONMENT=development
LOG_LEVEL=DEBUG

# Telegram Bot
TELEGRAM_BOT_TOKEN=**********************************************
TELEGRAM_BOT_USERNAME=Chart_Genius_bot

# Webhook (disabled for development)
WEBHOOK_MODE=false
WEBHOOK_HOST=0.0.0.0
WEBHOOK_PORT=8080
WEBHOOK_URL=
WEBHOOK_SECRET=

# Backend API
BACKEND_URL=http://backend-v2:8001/api/v2
BACKEND_TIMEOUT=30

# Redis
REDIS_URL=redis://redis:6379/3

# WebApp
WEBAPP_URL=http://localhost:3001

# Rate limiting
RATE_LIMIT_MESSAGES=20
RATE_LIMIT_WINDOW=60

# Admin users (from ТЗ)
ADMIN_USER_IDS=299820674

# Features
ENABLE_INLINE_MODE=true
ENABLE_WEBAPP=true
ENABLE_NOTIFICATIONS=true

# Metrics
ENABLE_METRICS=true
METRICS_PORT=8080
